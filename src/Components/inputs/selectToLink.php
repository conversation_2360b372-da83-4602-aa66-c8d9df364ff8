<?php

/**
 * MultiSelect component for selecting more options graphically and with additional functionalities
 **/

class SelectToLink
{
    public static function render(string $placeholder, $arrayOfOptions, string $id, array $links = [], string $value = ""): string
    {
        return self::build($placeholder, $arrayOfOptions, $id, $links, $value);
    }

    public static function build(string $placeholder, array $arrayOfOptions, string $id, array $optionText = [], string $value = ""): string
    {
        $select = "<div id='" . $id . "Picker' class=' relative '>
                            <button id='" . $id . "MultiSelectResetAllButton' type='button'
                                    class='absolute multiselectresetall hidden bg-gray-400 hover:bg-red-500 cursor-pointer p-0.5 rounded-full text-white'
                                    style='left: -4px; top: -4px;'>
                                <svg class='w-3 h-3' aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='24'
                                     height='24' fill='none' viewBox='0 0 24 24'>
                                    <path stroke='currentColor' stroke-linecap='round' stroke-linejoin='round'
                                          stroke-width='2'
                                          d='M5 7h14m-9 3v8m4-8v8M10 3h4a1 1 0 0 1 1 1v3H9V4a1 1 0 0 1 1-1ZM6 7h12v13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V7Z'/>
                                </svg>
                            </button>
                            <div id='" . $id . "MultiSelectWrapper'
                                 class='text-md shadow-md text-gray-800 MultiSelectWrapper h-full rounded-lg p-2 px-4 cursor-pointer font-semibold flex justify-between items-center bg-purple-400 hover:bg-purple-800 transition-all text-white gap-2'>
                                <svg class='w-5 h-5 text-white' aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='24' height='24' fill='currentColor' viewBox='0 0 24 24'>
                                <path fill-rule='evenodd' d='M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm11-4.243a1 1 0 1 0-2 0V11H7.757a1 1 0 1 0 0 2H11v3.243a1 1 0 1 0 2 0V13h3.243a1 1 0 1 0 0-2H13V7.757Z' clip-rule='evenodd'/>
                                    </svg>
                                 <div id='" . $id . "MultiSelectPickerInitial'>$placeholder</div>
                                <div id='" . $id . "MultiSelectPickerList'
                                     class='flex flex-wrap gap-1 items-center m-1.5'>

                                </div>
                                <svg id='" . $id . "MultiSelectSvg' class='w-4 h-4 text-white' aria-hidden='true'
                                     xmlns='http://www.w3.org/2000/svg' width='24' height='24' fill='none'
                                     viewBox='0 0 24 24'>
                                    <path id='MultiSelectPath' stroke='currentColor' stroke-linecap='round'
                                          stroke-linejoin='round'
                                          stroke-width='2' d='m19 9-7 7-7-7'/>
                                </svg>
                            </div>
                            <div id='" . $id . "multiselectDropdown' style='width: 100%; -ms-overflow-style: none;
                                scrollbar-width: none;'
                                 class='absolute bg-white hidden z-10 transition-all shadow-xl max-h-72 overflow-y-scroll rounded-lg'>
                                <div class='flex flex-col bg-white mt-2 p-2 pt-0'>";

        foreach ($arrayOfOptions as $key => $item) {
            $select .= "<span hx-get='/investicne-zamery/pridat/" . $optionText[$key] . "' hx-target='#pageContentMain' hx-replace-url='true' class='cursor-pointer rounded-md p-2 " . $key . "MultiSelectSpan font-normal hover:bg-gray-300 hover:font-bold transition-all'>" . $item . "</span>";
        }

        $select .= "</div>
            </div>
        </div></div>";
        $select .= "<script src='/src/assets/js/selectToLink.js'></script>";
        $select .= "<script>
            document.addEventListener('DOMContentLoaded', function() {
                initializeMultiSelect('" . $id . "');
            });
            htmx.onLoad(function(content) {
            function initializeMultiSelect(id) {
                let optionsArr = [];
                let optionsAll = [];
                window.addEventListener('click', function (e) {
                    if (e.target.id !== '') {
                        if (!e.target.id.includes(id + 'MultiSelect')) {
                            document.getElementById(id + 'multiselectDropdown').style.display = 'none';
                        } else {
                            document.getElementById(id + 'multiselectDropdown').style.display = 'block';
                        }
                    } else {
                        document.getElementById(id + 'multiselectDropdown').style.display = 'none';
                    }
                });
                document.getElementById(id + 'MultiSelectWrapper').addEventListener('click', function (event) {
                    let dropdown = document.getElementById(id + 'multiselectDropdown');
                    $(dropdown).toggle();
                    document.getElementById(id + 'search').focus();
                });
                document.getElementById(id + 'multiselectDropdown').addEventListener('click', function (e) {
                    e.stopPropagation();
                });
            }

               initializeMultiSelect('" . $id . "');
           });
        </script>";

        return $select;
    }
}