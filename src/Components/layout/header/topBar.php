<?php
session_start();
require_once("/home/<USER>/www/conf/settings.php");
require_once("/home/<USER>/www/src/lib/connection.php");

$userID = $_SESSION["user"]["data"]["userid"];
$date = new DateTime("now", new DateTimeZone('Europe/Bratislava'));
$today = $date->format('Y-m-d H:i:s T');
$checkLoginStatus = Connection::getDataFromDatabase("SELECT logged, forcelogout FROM users WHERE userid = $userID", defaultDB);

$mentionsQuery = "SELECT m.*, u.last_readed, u.username, u.usernick, i.username as initiatorusername, i.usernick as initiatorusernick
FROM mentions m
         JOIN users u ON u.last_readed < m.datetimeactivity AND u.userid = $userID AND
                         m.datetimeactivity < '$today'
         JOIN users i ON i.userid = m.initiatorid
WHERE mentionedid = $userID
ORDER BY m.datetimeactivity DESC";
$mentions = Connection::getDataFromDatabase($mentionsQuery, defaultDB);

$modeText = "Default";
$modeColor = "gray-400";

switch ($_SESSION["mode"]["mode"]) {
    case "global":
        $modeText = "Hromadný";
        $modeColor = "purple-400";
        break;
    case "client":
        $modeText = "Klient";
        $modeColor = "green-400";
        break;
    case "admin":
        $modeText = "Správca";
        $modeColor = "yellow-400";
        break;
    case "polaris":
        $modeText = "Polaris";
        $modeColor = "red-400";
        break;
    case "deposit":
        $modeText = "Deposit";
        $modeColor = "indigo-400";
        break;
}

if (!$checkLoginStatus[1][0]["logged"]) {
    unset($_SESSION['user']);
    session_destroy();
    header("Location: /");
}

if ($checkLoginStatus[1][0]["forcelogout"]) {
    unset($_SESSION['user']);
    session_destroy();
    header("Location: /?logoutKilled=1");
}


if (!isset($content)) {
    $content = '<p>There is nothing to show</p>';
}
if (isset($_POST["path"])) {
    $url = $_POST["path"];
} else {
    $url = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
}

switch ($url) {
    case str_contains($_SERVER["REQUEST_URI"], "dashboard"):
        $urlHeading = "Domov";
        break;
    case str_contains($_SERVER["REQUEST_URI"], "klientsky-vypis"):
        $urlHeading = "Klientský výpis";
        break;
    case str_contains($_SERVER["REQUEST_URI"], "poplatky"):
        $urlHeading = "Prehľad poplatkov";
        break;
    case str_contains($_SERVER["REQUEST_URI"], "vyplatene-vynosy"):
        $url = "vyplatene-vynosy";
        $urlHeading = "Prehľad vyplatených výnosov";
        break;
    case str_contains($_SERVER["REQUEST_URI"], "nbs-gfi"):
        $urlHeading = "Reporty pre NBS a GFI";
        break;
    case str_contains($_SERVER["REQUEST_URI"], "zmluvy-klienti"):
        $urlHeading = "Zmluvy a klienti";
        break;
    case str_contains($_SERVER["REQUEST_URI"], "nastavenia"):
        $urlHeading = "Nastavenia";
        break;
    case str_contains($_SERVER["REQUEST_URI"], "obchodny-dennik"):
        $urlHeading = "Obchodný denník";
        break;
    case str_contains($_SERVER["REQUEST_URI"], "konstanty/staty"):
        $urlHeading = "Nastavenia";
        $pageHeading = "Správa štátov";
        $constantsTable = "state";
        $constantsOrderBy = "stateid";
        break;
    case str_contains($_SERVER["REQUEST_URI"], "investicne-zamery"):
        $url = "investicne-zamery";
        $urlHeading = "Investičné zámery";
        break;
    case str_contains($_SERVER["REQUEST_URI"], "transakcie"):
        $url = "transakcie";
        $urlHeading = "Transakcie";
        break;
    case str_contains($_SERVER["REQUEST_URI"], "presun-prostriedkov"):
        $url = "presun-prostriedkov";
        $urlHeading = "Presun prostriedkov";
        break;
    case str_contains($_SERVER["REQUEST_URI"], "sankcny-zoznam"):
        $url = "sankcny-zoznam";
        $urlHeading = "Sankčný zoznam";
        break;
    case str_contains($_SERVER["REQUEST_URI"], "vysporiadanie"):
        $url = "vysporiadanie";
        $urlHeading = "Vysporiadanie";
        break;
    case str_contains($_SERVER["REQUEST_URI"], "klienti/detail"):
        $url = "klienti";
        $urlHeading = "Detail klienta";
        break;
    case str_contains($_SERVER["REQUEST_URI"], "vypis-o-stave-majetku"):
        $url = "vypis-o-stave-majetku";
        $urlHeading = "Výpis o stave majetku";
        break;
    case str_contains($_SERVER["REQUEST_URI"], "menove-pary"):
        $url = "menove-pary";
        $urlHeading = "Menové páry";
        break;
    case str_contains($_SERVER["REQUEST_URI"], "dlhopisy/istina"):
    case str_contains($_SERVER["REQUEST_URI"], "dlhopisy/kupon"):
        $url = "/dlohopisy";
        $urlHeading = "Splatenie kupónov a istín";
        break;
    case str_contains($_SERVER["REQUEST_URI"], "vklad-prostriedkov"):
        $url = "/vklad-prostriedkov";
        $urlHeading = "Vklad prostriedkov";
        break;
    default:
        $urlHeading = "Domov";
        break;
} ?>
<div class="flex items-center w-full gap-10">
    <section class="flex gap-1 items-center w-full">
        <div class="flex justify-start gap-4 items-center">
            <button id="menuToggler" class="p-1 text-gray-600 hover:bg-gray-200 rounded-full cursor-pointer"
                data-drawer-target="drawer-navigation">
                <svg class="w-5 h-5 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                    width="24" height="24" fill="none" viewBox="0 0 24 24">
                    <path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M5 7h14M5 12h14M5 17h10" />
                </svg>

            </button>
        </div>
        <h2 hx-get="<?php echo $_SERVER["REQUEST_URI"]; ?>" hx-target="#pageContentMain" hx-replace-url="true"
            class="font-bold text-2xl pl-5 text-gray-800 dark:text-gray-200">
            <?php echo $urlHeading; ?>
        </h2>
    </section>
    <form class="my-2 w-full">
        <label class="sr-only" for="topbar-search">Search</label>
        <div class="relative" id="topbar-searchWrapper">
            <div class="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
                <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg">
                    <path clip-rule="evenodd"
                        d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                        fill-rule="evenodd"></path>
                </svg>
            </div>
            <input onfocus="enableSearch()" onkeydown="clearGlobalSearchTimeout()" onkeyup="globalSearch(event)"
                class="text-gray-900 dark:text-gray-100 w-60 text-sm font-semibold rounded-xl focus:ring-primary-500 focus:border-primary-500 block w-full pl-10 p-2.5 border-0 bg-gray-100 dark:bg-gray-700 dark:placeholder-gray-400"
                id="topbar-search" placeholder="Vyhľadávanie..." type="text" />
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-circle-x-icon absolute right-4 top-3 text-gray-400 hover:text-gray-100 transition-all cursor-pointer lucide-circle-x">
                <circle cx="12" cy="12" r="10" />
                <path d="m15 9-6 6" />
                <path d="m9 9 6 6" />
            </svg>
        </div>
    </form>
    <?php if (isset($_SESSION["client"])) { ?>
        <div class="w-full">
            <div
                class="w-full flex group dark:text-gray-100 relative max-w-sm bg-white border dark:hover:bg-gray-700 transition-all cursor-pointer border-gray-200 rounded-lg py-1 px-2 shadow-sm dark:bg-gray-800 dark:border-gray-700">
                <div hx-post="/api/unsetClient"
                    class="absolute bg-red-700 hover:bg-red-800 cursor-pointer transition-all opacity-0 group-hover:opacity-100 rounded-full p-0.5 -right-2 -top-1">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-x">
                        <path d="M18 6 6 18" />
                        <path d="m6 6 12 12" />
                    </svg>
                </div>
                <a href="/klienti/detail/<?php echo $_SESSION["client"]["podielnikid"]; ?>"
                    class="flex w-full items-center justify-between gap-2">
                    <div class="flex items-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-circle-user">
                            <circle cx="12" cy="12" r="10" />
                            <circle cx="12" cy="10" r="3" />
                            <path d="M7 20.662V19a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v1.662" />
                        </svg>
                        <div class="">
                            <h5 class="mb-1 text-sm font-medium text-gray-900 dark:text-white">
                                <?php echo $_SESSION["client"]["meno"]; ?>
                            </h5>
                            <span
                                class="text-xs text-gray-500 dark:text-gray-400"><?php echo $_SESSION["client"]["podielnikid"]; ?></span>
                            <small>|</small>
                            <span
                                class="text-xs text-gray-500 dark:text-gray-400"><?php echo $_SESSION["client"]["fondid"]; ?></span>
                        </div>
                    </div>
                    <?php $cislozmluvy = Connection::getDataFromDatabase("SELECT cislozmluvy FROM portfolio WHERE fondid = " . $_SESSION["client"]["fondid"], defaultDB)[1][0]["cislozmluvy"]; ?>
                    <span class="font-bold px-3"><?php echo $cislozmluvy ?></span>
                </a>
            </div>
        </div>
    <?php } ?>
    <div class="flex items-center justify-end gap-1 w-full lg:order-2">
        <button aria-controls="drawer-navigation"
            class="p-2 mr-1 text-gray-500 rounded-lg md:hidden hover:text-gray-900 hover:bg-gray-300 dark:text-gray-400 dark:hover:text-white dark:hover:bg-gray-700 focus:ring-4 focus:ring-gray-300 dark:focus:ring-gray-600"
            data-drawer-toggle="drawer-navigation" type="button">
            <span class="sr-only">Toggle search</span>
            <svg aria-hidden="true" class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg">
                <path clip-rule="evenodd"
                    d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                    fill-rule="evenodd"></path>
            </svg>
        </button>
        <button
            class="p-2 flex items-center cursor-pointer gap-1 dark:text-white text-gray-500 relative rounded-lg hover:text-gray-900 hover:bg-gray-200 transition-all dark:text-gray-400 dark:hover:text-white dark:hover:bg-gray-700 focus:ring-4 focus:ring-gray-300 dark:focus:ring-gray-600"
            data-dropdown-toggle="apps-dropdown" data-tooltip-placement="bottom" data-tooltip-target="tooltip-bottom"
            type="button">
            <small class="mr-2"><strong
                    class="px-2 py-1 <?php echo "bg-" . $modeColor; ?> text-white dark:text-gray-800 text-xs rounded-md"><?php echo $modeText; ?></strong></small>
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z">
                </path>
            </svg>
        </button>
        <div class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700"
            id="tooltip-bottom" role="tooltip">
            Režimy aplikácie
            <div class="tooltip-arrow" data-popper-arrow></div>
        </div>
        <?php include "/home/<USER>/www/src/Components/layout/header/modeSwitcher.php"; ?>
        <?php include "/home/<USER>/www/src/Components/layout/header/themeToggle.php"; ?>
        <?php include "/home/<USER>/www/src/Components/layout/header/notificationsTrigger.php"; ?>
        <button aria-expanded="false" onclick="openUserMenu(event)"
            class="p-1 dark:text-gray-100 text-gray-500 relative rounded-lg cursor-pointer hover:text-gray-900 dark:hover:bg-gray-400 hover:bg-gray-200 transition-all focus:ring-4 focus:ring-gray-300"
            data-dropdown-toggle="dropdown" id="user-menu-button" type="button">
            <span class="sr-only">Open user menu</span>
            <svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-circle-user-round">
                <path d="M18 20a6 6 0 0 0-12 0" />
                <circle cx="12" cy="10" r="4" />
                <circle cx="12" cy="12" r="10" />
            </svg>
        </button>
    </div>
</div>