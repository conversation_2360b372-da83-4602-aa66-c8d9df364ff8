<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$dividendaTyp = $_POST["dividendaTyp"];
$datum_naroku = $_POST["datum-naroku"] ? $_POST["datum-naroku"] : $_POST["datum"];
$mena = $_POST["secondParam"] ? $_POST["secondParam"] : $_POST["mena"];
$isincurr = $_POST["isin"] ? $_POST["isin"] : $_POST["isincurrvalues"];
$hotovost = $_POST["hotovost"];
$sumaNaKus = $_POST["suma"] ? $_POST["suma"] : $_POST["vynos-na-kus"];
$action = $_POST["action"];
$hrubyVynos = $_POST["hruby-vynos"];
$cistyVynos = $_POST["cisty-vynos"];
$dan = $_POST["dan"];
$ucetaktiva = $_POST["ucetaktiva"];
$faktor = $_POST["faktor"];
$pocetkusov = $_POST["pocetkusov"];
$frakcia = $_POST["frakcia"];
$krajinaPovodu = $_POST["krajinaPovodu"] ? $_POST["krajinaPovodu"] : $_POST["kraj-povodu"];
$danSadzba = $_POST["danSadzba"] ? $_POST["danSadzba"] : $_POST["sadzba-dane"];
$vs = $_POST["vs"] ? $_POST["vs"] : $_POST["vsvalues"];
$cub = $_POST["cub"] ? $_POST["cub"] : $_POST["cubvalues"];

$harakterDividendy = Connection::getDataFromDatabase("SELECT hotovost, md_d, (select to_char(max(datum),'dd.mm.yyyy') from today) as todayDate
 FROM dividendaakciatyp WHERE subkodobratu = $dividendaTyp", defaultDB)[1][0];
$hotovostna = $harakterDividendy["hotovost"];
$md_d = $harakterDividendy["md_d"];
$todayDate = $harakterDividendy["todayDate"];

if (isset($_SESSION["client"])) {
    $fondid = $_SESSION["client"]["fondid"];
} elseif ($_SESSION["mode"]["mode"] === "admin") {
    $fondid = 1;
} else {
    $fondid = 0;
}

//zistenie ci sa jedna o datum z today alebo z majetoktotal
$datumek = Connection::getDataFromDatabase("SELECT (case when max(datum) = '$datum_naroku' then 1
					 else 0
				end) as today,
				MAX(datum) as datzauctovania
		from today where confirmed='1'", defaultDB)[1][0];

$today = $datumek["today"];
$datuazuctovania = $datumek["datzauctovania"];

if (isset($realneSplatenie) and $realneSplatenie != "") {
    $today = 1;
}

if (!$today) {
    //datum nie je z today, citam z majetoktotal stav akcii, jedna sa o natipovanie pohladavok
    $table_qry = " majetoktotal ";
    $where_qry = " 
	 m.datum = '$datum_naroku'::date and 
	 uctovnykod in (251200) and ";
} else {
    $table_qry = " majetoktoday ";
    $where_qry = " m.uctovnykod in (251200) and ";
}

if ($datum_naroku != "" and $datum_naroku != $datuazuctovania) {
    $table_qry2 = "majetoktotal";
    $where_qry2 = " m.datum = '$datum_naroku'::DATE and";

} else {
    $table_qry2 = $table_qry;
    $where_qry2 = "";
}

if ($id_fond > 0) {
    $where_qry .= " m.subjektid = $id_fond and ";
} else {
    $where_qry .= " ('$isincurr' = dcurr.isin OR '$isincurr' = dcurr.isincurr) AND ";
}

if ($registraciaFrakcii == "ano") {
    $query_frakcieFonds = " m.subjektid in ($frakcieFonds) and";
}


$bigQuery = "SELECT
p.podielnikid,
				po.cislozmluvy as cislozmluvy,
	   		m.ucetaktiva,
		   	dcurr.isin as isin, 
		   	m.kodaktiva,
	   		po.fondid,
				dric.isincurrric,
				dcurr.currencytrade,
				max(m.mena" . ((!$today) ? "denom" : "") . ") as mena,
			
	   		sum(m.pocet*SIGN(m.md_d-0.5)*(-1)) as suma,
	   		COALESCE(max(total_pf.celkovo)/MAX(total.celkovo),0) as faktor,
	   		max(total_pf.celkovo) as pocetkusov,    
	   		max(p.fpo) as fpo, 
				max(
					case when p.fpo = 0 then de.sadzbafo/100
							 when p.fpo = 1 then de.sadzbapo/100
					else de.sadzbano
					end
				) as danSadzba,
				e.emitentstateid as krajinaPovodu,
                SUM(sum(m.pocet * SIGN(m.md_d - 0.5) * (-1))) OVER (PARTITION BY currencytrade)
		from 	
				podielnik p, 
				portfolio po, 
				$table_qry m, 
				dbequitycurrric dric, 
				dbequitycurr dcurr, 
				dbequity de,
				equityemitent e,
				(
 				SELECT 
 					SUM(pocet*SIGN(md_d-0.5)*(-1)) AS celkovo, kodaktiva 
 				FROM 
 					$table_qry2 m
 				where
 					$where_qry2
 					uctovnykod in (251200,315620,315625,315615, 315612,315613,315614,315633)
 				GROUP BY kodaktiva 
				) total,
				(
				select 
					SUM(pocet*SIGN(md_d-0.5)*(-1)) AS celkovo, kodaktiva, subjektid 
 				FROM 
 					$table_qry2 m
 				where
 					$where_qry2
 					uctovnykod in (251200,315620,315625, 315615, 315612,315613,315614,315633)
 				GROUP BY kodaktiva,subjektid 
				) total_pf
		where 
			  p.podielnikid = po.podielnikid and
			  po.fondid = m.subjektid and
			  dric.isincurr = dcurr.isincurr and
			  e.emitentid = de.emitentid and
				$where_qry
			  $query_frakcieFonds
			  m.kodaktiva = dric. isincurrric and
			  de.isin = dcurr.isin and
			  total.kodaktiva = m.kodaktiva and
			  total_pf.kodaktiva = m.kodaktiva and
			  total_pf.subjektid = m.subjektid AND
            m.mena" . ((!$today) ? "denom" : "") . " = '$mena'
			  
		group by
			    p.podielnikid, po.cislozmluvy, m.ucetaktiva, dcurr.isin, m.kodaktiva,dcurr.currencytrade, 
		   	  po.fondid,	isincurrric, e.emitentstateid
		order by 
				po.cislozmluvy";
$result = Connection::getDataFromDatabase($bigQuery, defaultDB)[1];
if (count($result) > 0) { ?>
    <div id="dividendyToScrollTo" class="relative overflow-x-auto z-0 p-10 shadow-md sm:rounded-lg">
        <div id="toast"></div>
        <h1 class="text-3xl dark:text-white mb-4 font-bold">Výber dividend</h1>
        <form id="splatenieDividendyForm">
            <table class="w-full table-fixed text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                    <tr>
                        <th scope="col" class="px-3 py-2">
                            Číslo zmluvy
                        </th>
                        <?php if ($action === "natip") { ?>
                            <th scope="col" class="px-3 py-2">
                                Počet kusov
                            </th>
                        <?php } ?>
                        <th scope="col" class="px-3 py-2">
                            Pohľadávka
                        </th>
                        <?php if (($action == "splatenie" && $hotovostna == 1) || $id_fond > 0) { ?>
                            <th scope="col" class="px-3 py-2">
                                Hrubý výnos
                            </th>
                            <th scope="col" class="px-3 py-2">
                                Daň
                            </th>
                            <th scope="col" class="px-3 py-2">
                                Čistý výnos
                            </th>
                        <?php }
                        if ($id_fond > 0) { ?>
                            <th scope="col" class="px-3 py-2">
                                Číslo účtu
                            </th>
                            <th scope="col" class="px-3 py-2">
                                VS
                            </th>
                            <th scope="col" class="px-3 py-2">
                                Výnos na kus
                            </th>
                            <th scope="col" class="px-3 py-2">
                                Sadzba dane
                            </th>
                        <?php } ?>
                        <th scope="col" class="px-3 py-2">
                            Mena
                        </th>
                        <th scope="col" class="px-3 py-2">
                            Akcia
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $sumaSpolu = 0;
                    $hurbyVynosSpolu = 0;
                    $danSpolu = 0;
                    $cistyVynosSpolu = 0;
                    $kusySpolu = 0;
                    foreach ($result as $key => $item) {
                        $sumaNaKusFixed = str_replace(',', '.', $sumaNaKus);
                        if ($hotovostna) {
                            $suma = (int) $item["pocetkusov"] * $sumaNaKusFixed;
                        } else {
                            $suma = $sumaNaKusFixed * (int) $item["pocetkusov"];

                            if ($divAkciaMD_D == 0 and $hotovostna == 0 and $action == "natip") {
                                $frakcia = ($sumaNaKusFixed * $item["pocetkusov"]) - $suma;
                                $sess_frakcie[$item['fondid']] = $frakcia;
                            }
                        }
                        $hrubyVyonsek = $hrubyVynos / $item["sum"] * $item["pocetkusov"];
                        $danek = $hrubyVyonsek * $danSadzba / 100;
                        $cistyVynosek = $hrubyVyonsek - $danek;

                        $hrubyVynosSpolu += $hrubyVyonsek;
                        $danSpolu += $danek;
                        $cistyVynosSpolu += $cistyVynosek;
                        $sumaSpolu += $suma;
                        $kusySpolu += $item["pocetkusov"];
                        ?>
                        <tr class="dark:bg-gray-800 bg-gray-50 border-b dividendaRow dark:border-gray-700 border-gray-200">
                            <th scope="row" class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                <?php echo $item["cislozmluvy"];
                                if (($action == "splatenie" and $hotovostna == 1) or $id_fond > 0) {
                                    echo "<br>" . $item["ucetaktiva"];
                                }
                                ?>
                            </th>
                            <?php if ($action === "natip") { ?>
                                <td class="px-4 py-2">
                                    <?php echo $item["pocetkusov"]; ?>
                                </td>
                            <?php } ?>
                            <td class="px-4 py-2">
                                <input type="text" name="suma" value="<?php echo number_format($suma, 2, ".", " "); ?>"
                                    step=".01" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500
                                 block p-1.5 dark:bg-gray-700 suma dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 
                                 dark:focus:border-blue-500" required />
                            </td>
                            <?php
                            if (($action == "splatenie" && $hotovostna == 1) || $id_fond > 0) { ?>
                                <td class="px-4 py-2">
                                    <input type="text" name="hrubyvynos" value="<?php echo $hrubyVyonsek; ?>" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500
                                 block p-1.5 dark:bg-gray-700 hrubyvynos dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 
                                 dark:focus:border-blue-500" required />
                                </td>
                                <td class="px-4 py-2">
                                    <input type="text" name="dan" value="<?php echo $danek; ?>" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500
                                 block p-1.5 dark:bg-gray-700 danInput dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 
                                 dark:focus:border-blue-500" required />
                                </td>
                                <td class="px-4 py-2">
                                    <input type="text" name="cistyvynos" value="<?php echo $cistyVynosek; ?>" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500
                                 block p-1.5 dark:bg-gray-700 cistyvynos dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 
                                 dark:focus:border-blue-500" required />
                                </td>
                            <?php }
                            if ($id_fond > 0) { ?>
                                <td class="px-4 py-2">
                                    <div>
                                        <label for="ucet" class="block text-sm font-medium dark:text-gray-100 mb-2">Účet</label>
                                        <select id="ucet" name="cubFond" class="w-full px-4 py-3 border border-gray-300 dark:bg-gray-900 rounded-lg bg-white dark:text-gray-100 focus:outline-none
                             focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                            <?php
                                            $ucty = Connection::getDataFromDatabase("SELECT distinct cub, mena from fondsbu order by cub", defaultDB)[1];
                                            foreach ($ucty as $key => $item) { ?>
                                                <option value="<?php echo $item["cub"]; ?>"><?php echo $item["cub"]; ?>
                                                </option>
                                            <?php } ?>
                                        </select>
                                    </div>
                                </td>
                                <td class="px-4 py-2">
                                    <input type="text" id="vs" name="vs" value="<?php echo $vs; ?>" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500
                                 block p-1.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 
                                 dark:focus:border-blue-500" required />
                                </td>
                                <td class="px-4 py-2">
                                    <input type="text" id="nakus" name="nakus" value="<?php echo $item["nakus"]; ?>" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500
                                 block p-1.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 
                                 dark:focus:border-blue-500" required />
                                </td>
                                <td class="px-4 py-2">
                                    <input type="text" id="danSadzba" name="danSadzba" value="<?php echo $item["dansazdba"]; ?>"
                                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500
                                 block p-1.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 
                                 dark:focus:border-blue-500" required />
                                </td>
                            <?php } else { ?>
                                <input type="hidden" name="cubFond" value="<?php echo $cub; ?>" />
                            <?php } ?>
                            <td class="px-4 py-2">
                                <?php echo $item["mena"]; ?>
                            </td>
                            <td class="px-4 py-2 flex items-center gap-2">
                                <input type="hidden" name="cislozmluvy" value="<?php echo $item["cislozmluvy"] ?>" />
                                <input type="hidden" name="ucetaktiva" value="<?php echo $item["ucetaktiva"] ?>" />
                                <input type="hidden" name="faktor" value="<?php echo $item["faktor"] ?>" />
                                <input type="hidden" name="pocetkusov" value="<?php echo $item["pocetkusov"] ?>" />
                                <input type="hidden" name="nakus" value="<?php echo $sumaNaKus ?>" />
                                <input type="hidden" name="frakcia" value="<?php echo $frakcia; ?>" />
                                <input type="hidden" name="krajinaPovodu" value="<?php echo $krajinaPovodu ?>" />
                                <input type="hidden" name="fond_id" value="<?php echo $item["fondid"] ?>" />
                                <input type="hidden" name="hotovostna" value="<?php echo $hotovostna; ?>" />
                                <input type="hidden" name="mena" value="<?php echo $item["mena"]; ?>" />
                                <input type="hidden" name="md_d" value="<?php echo $md_d; ?>" />
                                <input type="hidden" name="datum" value="<?php echo $todayDate; ?>" />
                                <input type="hidden" name="dividendaTyp" value="<?php echo $dividendaTyp; ?>" />
                                <input type="hidden" name="kodaktiva" value="<?php echo $item["kodaktiva"]; ?>" />
                                <input type="hidden" name="vs" value="<?php echo $vs; ?>" />
                                <?php if ($id_fond == 0) { ?>
                                    <input type="hidden" name="danSadzba" value="<?php echo $danSadzba; ?>" />
                                <?php } ?>
                            </td>
                        </tr>
                    <?php } ?>
                    <tr class="dark:bg-gray-900 bg-gray-300">
                        <td class="px-4 py-2">
                            <p>Spolu:</p>
                        </td>
                        <?php
                        if ($action === "natip") { ?>
                            <td class="px-4 py-2">
                                <p><?php echo $kusySpolu; ?></p>
                            </td>
                        <?php } else { ?>
                            <td class="px-4 py-2">
                                <input type="text" id="sumaSpolu" name="sumaSpolu"
                                    value="<?php echo number_format($sumaSpolu, 2, ".", " "); ?>" step=".01" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500
                                 block p-1.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 
                                 dark:focus:border-blue-500" required />
                            </td>
                        <?php } ?>

                        <?php
                        if ($action === "natip") { ?>
                            <td class="px-4 py-2">
                                <input type="text" id="sumaSpolu" name="sumaSpolu"
                                    value="<?php echo number_format($sumaSpolu, 2, ".", " "); ?>" step=".01" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500
                                 block p-1.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 
                                 dark:focus:border-blue-500" required />
                            </td>
                        <?php } else { ?>
                            <td class="px-4 py-2">
                                <input type="text" id="hrubyVynosSpolu" name="hrubyVynosSpolu"
                                    value="<?php echo number_format($hrubyVynosSpolu, 2, ".", " "); ?>" step=".01" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500
                                 block p-1.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 
                                 dark:focus:border-blue-500" required />
                            </td>
                        <?php } ?>
                        <?php if ($action === "natip") { ?>
                            <td class="px-4 py-2">
                                <?php echo $item["mena"]; ?>
                            </td> <?php } else { ?>
                            <td class="px-4 py-2">
                                <input type="text" id="danSpolu" name="danSpolu"
                                    value="<?php echo number_format($danSpolu, 2, ".", " "); ?>" step=".01" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500
                                 block p-1.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 
                                 dark:focus:border-blue-500" required />
                            </td>
                            <td class="px-4 py-2">
                                <input type="text" id="cistyVynosSpolu" name="cistyVynosSpolu"
                                    value="<?php echo number_format($cistyVynosSpolu, 2, ".", " "); ?>" step=".01" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500
                                 block p-1.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 
                                 dark:focus:border-blue-500" required />
                            </td>
                            <td class="px-4 py-2">
                                <?php echo $item["mena"]; ?>
                            </td>
                        <?php } ?>
                        <td class="px-4 py-2">
                            <button type="submit"
                                class="p-1 px-2 rounded-md hover:bg-gray-200 flex items-center gap-2 dark:hover:bg-gray-700 transition-all cursor-pointer">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-circle-check-icon lucide-circle-check">
                                    <circle cx="12" cy="12" r="10" />
                                    <path d="m9 12 2 2 4-4" />
                                </svg>
                                <?php echo $action == "natip" ? "Natipovať" : "Splatiť"; ?>
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </form>
    </div>
    <script>
        function scrollToDividendy() {
            const dividendyToScrollTo = document.getElementById('dividendyToScrollTo');
            dividendyToScrollTo.scrollIntoView({ behavior: 'smooth' });
        }
        window.onload = scrollToDividendy();
    </script>
    <script>
        function sanitazeInput(e) {
            if (e.target.value === "" || e.target.value < 0) {
                e.target.value = "";
            }
            if (e.target.value.includes(",")) {
                e.target.value = e.target.value.replace(",", ".");
            }
            if (e.target.value.includes(" ")) {
                e.target.value = e.target.value.replace(" ", "");
            }
            if (e.target.value != "") {
                setTimeout(() => {
                    e.target.value = parseFloat(e.target.value).toFixed(2);
                }, 500);
            }
        }

        $(".suma").on("keyup", (e) => {
            const allSumaInputs = document.querySelectorAll(".suma");
            setTimeout(() => {
                sanitazeInput(e);
                let sum = 0;
                allSumaInputs.forEach((item) => {
                    sum = sum + parseFloat(item.value);
                });
                $("#sumaSpolu").val(sum.toFixed(2));
            }, 500);
        });

        $(".hrubyvynos").on("keyup", (e) => {
            const allHrubyVynosInputs = document.querySelectorAll(".hrubyvynos");
            setTimeout(() => {
                sanitazeInput(e);
                let sum = 0;
                allHrubyVynosInputs.forEach((item) => {
                    sum = sum + parseFloat(item.value);
                });
                $("#hrubyVynosSpolu").val(sum.toFixed(2));
            }, 500);
        });

        $(".danInput").on("keyup", (e) => {
            const allDanInputs = document.querySelectorAll(".danInput");
            const allCistyVynosInputs = document.querySelectorAll(".cistyvynos");
            const allHrubyVynosInputs = document.querySelectorAll(".hrubyvynos");
            setTimeout(() => {
                sanitazeInput(e);
                let sum = 0;
                let danove = [];
                let cistyVynosSum = 0;
                allDanInputs.forEach((item) => {
                    sum = sum + parseFloat(item.value);
                    danove.push(parseFloat(item.value));
                });
                for (let i = 0; i < allCistyVynosInputs.length; i++) {
                    console.log(parseFloat(allHrubyVynosInputs[i].value) - parseFloat(danove[i]));
                    console.log(document.getElementById("hrubyVynosSpolu").value, danove[i]);
                    allCistyVynosInputs[i].value = parseFloat(allHrubyVynosInputs[i].value) - parseFloat(danove[i]);
                    cistyVynosSum += parseFloat(allCistyVynosInputs[i].value);
                }
                $("#cistyVynosSpolu").val(cistyVynosSum);
                $("#danSpolu").val(sum.toFixed(2));
            }, 500);
        });

        $(".cistyvynos").on("keyup", (e) => {
            const allCistyVynosInputs = document.querySelectorAll(".cistyvynos");
            setTimeout(() => {
                sanitazeInput(e);
                let sum = 0;
                allCistyVynosInputs.forEach((item) => {
                    sum = sum + parseFloat(item.value);
                });
                $("#cistyVynosSpolu").val(sum.toFixed(2));
            }, 500);
        });

        $("#splatenieDividendyForm").on("submit", (e) => {
            e.preventDefault();
            const formData = new FormData(e.currentTarget);
            console.log(formData);
            const transactionsCount = document.querySelectorAll(".dividendaRow").length;

            const suma = formData.getAll("suma");
            const hrubyVynos = formData.getAll("hrubyvynos");
            const dan = formData.getAll("dan");
            const cistyVynos = formData.getAll("cistyvynos");
            const cubFond = formData.getAll("cubFond");
            const cislozmluvy = formData.getAll("cislozmluvy");
            const ucetaktiva = formData.getAll("ucetaktiva");
            const faktor = formData.getAll("faktor");
            const pocetkusov = formData.getAll("pocetkusov");
            const nakus = formData.getAll("nakus");
            const frakcia = formData.getAll("frakcia");
            const krajinaPovodu = formData.getAll("krajinaPovodu");
            const fond_id = formData.getAll("fond_id");
            const mena = formData.getAll("mena");
            const md_d = formData.getAll("md_d");
            const datum = formData.getAll("datum");
            const dividendaTyp = formData.getAll("dividendaTyp");
            const kodaktiva = formData.getAll("kodaktiva");
            const vs = formData.getAll("vs");
            const danSadzba = formData.getAll("danSadzba");
            const hotovostna = formData.getAll("hotovostna");

            const data = [];

            for (let i = 0; i < transactionsCount; i++) {
                data.push({
                    suma: suma[i],
                    hrubyVynos: hrubyVynos[i],
                    dan: dan[i],
                    cistyVynos: cistyVynos[i],
                    cubFond: cubFond[i],
                    cislozmluvy: cislozmluvy[i],
                    ucetaktiva: ucetaktiva[i],
                    faktor: faktor[i],
                    pocetkusov: pocetkusov[i],
                    nakus: nakus[i],
                    frakcia: frakcia[i],
                    krajinaPovodu: krajinaPovodu[i],
                    fond_id: fond_id[i],
                    mena: mena[i],
                    md_d: md_d[i],
                    datum: datum[i],
                    dividendaTyp: dividendaTyp[i],
                    kodaktiva: kodaktiva[i],
                    vs: vs[i],
                    danSadzba: danSadzba[i],
                    hotovostna: hotovostna[i]
                });
            }

            console.log(data);

            htmx.ajax('POST', `/api/akcie/dividendy/<?php echo $action === "natip" ? "natipovat" : "splatit"; ?>`, {
                target: "#toast",
                values: {
                    "data": JSON.stringify(data)
                }
            }).then((response) => {

            });

        });

    </script>
<?php }
