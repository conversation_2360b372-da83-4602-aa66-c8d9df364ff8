<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";


$data = json_decode($_POST["data"], true);
$queryPool = [];


foreach ($data as $key => $item) {
    $kodobratu = "280";
    $suma = (int)$data[$key]["suma"];
    $hrubyVynos = $data[$key]["hrubyVynos"];
    $cistyVynos = $data[$key]["cistyVynos"];
    $dan = $data[$key]["dan"];
    $ucetaktiva = $data[$key]["ucetaktiva"];
    $faktor = $data[$key]["faktor"];
    $pocetkusov = $data[$key]["pocetkusov"];
    $frakcia = $data[$key]["frakcia"];
    $krajinaPovodu = $data[$key]["krajinaPovodu"];
    $hotovostna = $data[$key]["hotovostna"];
    $mena = $data[$key]["mena"];
    $md_d = $data[$key]["md_d"];
    $datum = $data[$key]["datum"];
    $dividendaTyp = $data[$key]["dividendaTyp"];
    $fond_id = $data[$key]["fond_id"];
    $kodaktiva = $data[$key]["kodaktiva"];

    $obratID = Connection::getDataFromDatabase("SELECT nextval('majetok_today_obratid')", defaultDB)[1][0]["nextval"];

    if ($hotovostna == 0) {
        $cub_bu = $data[$key]["cub_bu"];
    } else {
        $fond_id = $data[$key]["fond_id"];
    }

    if ($hotovostna) {
        $queryUcet = "(select cub from fondsbu where mena = '$mena' and fondid=$fond_id and rownum=1)";
        $jednotka = $mena;
    } else {
        $queryUcet = "'" . ${"ucetaktiva" . $i} . "'";
        $jednotka = "ks";

    }
    $ucetAktivaDividenda = Connection::getDataFromDatabase("SELECT last_value FROM s_ucetaktiva_dividenda;", defaultDB)[1][0]["last_value"];
    if ($hotovostna) {
        $queryUcet = "'HD-'" . " || lpad('$ucetAktivaDividenda'::varchar,6,'0')";
        $ucetAktivaDividenda = Connection::getDataFromDatabase("SELECT nextval('S_UCETAKTIVA_DIVIDENDA')", defaultDB)[1][0]["nextval"];
    }

    $queryPool[] = [
        "query" => "INSERT INTO majetoktoday
    (obratid,destinacia,subjektid,kodobratu,
		 uctovnykod,eqid,kodaktiva,ucetaktiva,jednotka,
		 pocet,mena,md_d,obratdatetimereal,obratdatatimezauctovane)
		select
		 $obratID,'splatenieakcia',$fond_id,kodobratu,
		 uctovnykod,equid,'$kodaktiva', $queryUcet, '$jednotka',
		 $suma,'$mena',md_d,(select datum from today where fondid=$fond_id), (select datum from today where fondid=$fond_id)
		from kodobratumd_d where kodobratu in ($kodobratu) and subkodobratu=$dividendaTyp",
        "params" => [],
        "db" => defaultDB,
        "name" => "Insert into majetoktoday"
    ];
}
$transaction = Connection::runTransaction(defaultDB, null, $queryPool);
if (str_contains($transaction, "poriadku")) { ?>
    <div id="toast-success"
        class="flex border-green-300 border items-center w-full max-w-sm p-4 mb-4 text-green-500 bg-white rounded-lg shadow dark:text-gray-400 dark:bg-gray-800"
        role="alert">
        <div
            class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-green-500 bg-green-100 rounded-lg dark:bg-green-800 dark:text-green-200">
            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z" />
            </svg>
            <span class="sr-only">Check icon</span>
        </div>
        <div class="ms-3 text-sm font-semibold">Presun prostriedkov bol úspešne inicalizovaný.
        </div>
        <button type="button"
            class="ms-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
            data-dismiss-target="#toast-success" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
        </button>
    </div>
    <script>
        setTimeout(() => {
            htmx.ajax('GET', "/akcie/dividendy/natipovanie", {
                target: "#pageContentMain",
            });
        }, 1500);
    </script>
<?php } else {
    print_r($transaction);
}