<?php
require_once "/home/<USER>/www/src/lib/SimpleXLSXGen/SimpleXLSXGen.php";

$inputJSON = file_get_contents("php://input");
$data = json_decode($inputJSON, true);

$columns = json_decode($data["columns"]);
$rows = json_decode($data["data"]);
$filename = $data["filename"];
$property = $data["property"];

if (count($rows) === 2) {
    $suhrneUdaje = array_merge(
        [$columns],
        array_merge((array) $rows[1], (array) current((Array) $rows[0]))
    );
} else {
    $suhrneUdaje = array_merge(
        [$columns],
        $rows
    );
}

$xlsx = Shuchkin\SimpleXLSXGen::fromArray($suhrneUdaje, $filename);
$xlsx->saveAs("/home/<USER>/www/temp/" . $filename . ".xlsx");

header('Content-Type: application/json');
echo json_encode(["data" => current((Array) $rows[0]), "link" => '/home/<USER>/www/temp/' . $filename . ".xlsx"]);