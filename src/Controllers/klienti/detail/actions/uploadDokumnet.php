<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
$allowed = array("image/jpeg", "image/png", "application/pdf");
$rawData = file_get_contents('php://input');
function slugify($string)
{
    $string = iconv('UTF-8', 'ASCII//TRANSLIT', $string);
    $string = strtolower($string);
    $string = str_replace(' ', '_', $string);
    $string = preg_replace('/[^a-z0-9_]/', '', $string);
    return $string;
}
$meno = $_POST["meno"];
$directory = slugify($meno) . "_" . $_POST["user_id"];

$target_file = "/home/<USER>/www/temp/" . $directory . "/" . basename($_FILES["fileToUpload"]["name"]);
$uploadOk = 1;

if (!file_exists('/home/<USER>/www/temp/dokumenty/' . $directory)) {
    mkdir('/home/<USER>/www/temp/dokumenty/' . $directory, 0777, true);
}

if (isset($_FILES['fileToUpload']) && $_FILES['fileToUpload']['error'] === UPLOAD_ERR_OK) {
    $file_type = $_FILES['fileToUpload']['type'];
    if (in_array($file_type, $allowed)) {
        if (file_exists($target_file)) {
            $errorMsg = "Tento súbor už existuje";
            $uploadOk = 0;
        }

        if ($_FILES["fileToUpload"]["size"] > 5000000) {
            $errorMsg = "Nahrávaný súbor je príliš veľký.";
            $uploadOk = 0;
        }

        if ($uploadOk != 0) {
            if (move_uploaded_file($_FILES["fileToUpload"]["tmp_name"], $target_file)) {
                $insertToDokumenty = Connection::InsertUpdateCreateDelete("INSERT INTO dokumenty (datum, nazov_dokument, cesta, client_id, file_size, uploader_id, expiration_date) VALUES (?, ?, ?, ?, ?, ?, ?)", [
                    date("Y-m-d"),
                    $_FILES["fileToUpload"]["name"],
                    $target_file,
                    $_POST["user_id"],
                    $_FILES["fileToUpload"]["size"],
                    $_SESSION["user"]["data"]["userid"],
                    $_POST["expiration_date"]
                ], defaultDB);
                echo json_encode(["error" => false, "data" => $fileData, "result" => $_POST]);
                die();
            }
        } else {
            echo json_encode(["error" => true, "errorMsg" => $errorMsg, "kokot" => $uploadOk]);
        }
    } else {
        echo json_encode(["error" => true, "errorMsg" => "Tento typ súboru nie je povolený", "file" => $_POST]);
    }
} else {
    $errorMsg = "Upload error!";
    echo json_encode(["error" => true, "errorMsg" => $errorMsg, "kokot" => $_POST]);
}