<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

// Set content type for JSON response
header('Content-Type: application/json');

$allowed = array("image/jpeg", "image/png", "application/pdf");
$rawData = file_get_contents('php://input');
function slugify($string)
{
    $string = iconv('UTF-8', 'ASCII//TRANSLIT', $string);
    $string = strtolower($string);
    $string = str_replace(' ', '_', $string);
    $string = preg_replace('/[^a-z0-9_]/', '', $string);
    return $string;
}
// Check if required POST data exists
if (!isset($_POST["meno"]) || !isset($_POST["user_id"])) {
    echo json_encode(["error" => true, "errorMsg" => "Chýbajú povinné ú<PERSON>je (meno alebo user_id)", "post_data" => $_POST]);
    die();
}

$meno = $_POST["meno"];
$directory = slugify($meno) . "_" . $_POST["user_id"];

$target_file = "/home/<USER>/www/temp/dokumenty/" . $directory . "/" . basename($_FILES["fileToUpload"]["name"]);
$uploadOk = 1;

if (!file_exists('/home/<USER>/www/temp/dokumenty/' . $directory)) {
    mkdir('/home/<USER>/www/temp/dokumenty/' . $directory, 777, true);
}

if (isset($_FILES['fileToUpload']) && $_FILES['fileToUpload']['error'] === UPLOAD_ERR_OK) {
    $file_type = $_FILES['fileToUpload']['type'];
    if (in_array($file_type, $allowed)) {
        if (file_exists($target_file)) {
            $errorMsg = "Tento súbor už existuje";
            $uploadOk = 0;
        }

        if ($_FILES["fileToUpload"]["size"] > 5000000) {
            $errorMsg = "Nahrávaný súbor je príliš veľký.";
            $uploadOk = 0;
        }

        if ($uploadOk != 0) {
            if (move_uploaded_file($_FILES["fileToUpload"]["tmp_name"], $target_file)) {
                $insertToDokumenty = Connection::InsertUpdateCreateDelete("INSERT INTO dokumenty (datum, nazov_dokument, cesta, client_id, file_size, uploader_id, expiration_date) VALUES (?, ?, ?, ?, ?, ?, ?)", [
                    date("Y-m-d"),
                    $_FILES["fileToUpload"]["name"],
                    $target_file,
                    $_POST["user_id"],
                    $_FILES["fileToUpload"]["size"],
                    $_SESSION["user"]["data"]["userid"],
                    $_POST["expiration_date"] === "" ? null : $_POST["expiration_date"]
                ], defaultDB);
                echo json_encode(["error" => false, "data" => "File uploaded successfully", "result" => $_POST]);
                die();
            } else {
                echo json_encode(["error" => true, "errorMsg" => "Nepodarilo sa nahrať súbor na server", "kokot" => $uploadOk]);
                die();
            }
        } else {
            echo json_encode(["error" => true, "errorMsg" => $errorMsg, "kokot" => $uploadOk]);
            die();
        }
    } else {
        echo json_encode(["error" => true, "errorMsg" => "Tento typ súboru nie je povolený", "file" => $_POST]);
        die();
    }
} else {
    $upload_error = isset($_FILES['fileToUpload']['error']) ? $_FILES['fileToUpload']['error'] : 'No file uploaded';
    $errorMsg = "Upload error! Error code: " . $upload_error;
    echo json_encode(["error" => true, "errorMsg" => $errorMsg, "files_data" => $_FILES, "post_data" => $_POST]);
    die();
}