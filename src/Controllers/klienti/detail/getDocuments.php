<?php
require "/home/<USER>/www/src/lib/connection.php";
require "/home/<USER>/www/conf/settings.php";
$zastupujuci_vs = $_GET["zastupujuci_vs"];
$clientID = $_GET["id"];
$meno = $_GET["meno"];
$investicnyDotaznik = Connection::getDataFromDatabase("SELECT * FROM investicny_dotaznik WHERE user_id = $zastupujuci_vs", defaultDB)[1];
$dokumenty = Connection::getDataFromDatabase("SELECT d.*, u.username FROM dokumenty d JOIN users u ON u.userid = d.uploader_id WHERE client_id = $clientID", defaultDB)[1];
function formatBytes($bytes, $precision = 2)
{
    $units = array('B', 'KB', 'MB', 'GB', 'TB');

    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);

    // Uncomment one of the following alternatives
    $bytes /= pow(1024, $pow);
    // $bytes /= (1 << (10 * $pow)); 

    return round($bytes, $precision) . $units[$pow];
}
?>
<section class="bg-gray-50 dark:bg-gray-900 p-3 sm:p-5">
    <h2 class="text-4xl font-extrabold mb-6 dark:text-white">Dokumenty</h2>
    <div class="mx-auto">
        <div class="bg-white dark:bg-gray-800 relative shadow-md sm:rounded-lg overflow-hidden">
            <div class="flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-4">
                <div
                    class="w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-end md:space-x-3 flex-shrink-0">
                    <button type="button" data-modal-target="upload-file-modal" data-modal-toggle="upload-file-modal"
                        class="flex items-center justify-center text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">
                        <svg class="h-3.5 w-3.5 mr-2" fill="currentColor" viewbox="0 0 20 20"
                            xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                            <path clip-rule="evenodd" fill-rule="evenodd"
                                d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" />
                        </svg>
                        Pridať súbor
                    </button>
                    <?php if (count($investicnyDotaznik) == 0) { ?>
                        <button type="button"
                            class="flex items-center justify-center gap-2 text-gray-900 transition-all bg-orange-700 hover:bg-orange-900 focus:ring-4 focus:ring-orange-300 font-medium
                         rounded-lg text-sm px-4 py-2 dark:bg-orange-600 dark:hover:bg-orange-700 focus:outline-none dark:focus:ring-orange-800">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                class="lucide lucide-list-check-icon lucide-list-check">
                                <path d="M11 18H3" />
                                <path d="m15 18 2 2 4-4" />
                                <path d="M16 12H3" />
                                <path d="M16 6H3" />
                            </svg>
                            Pridať investičný dotazník
                        </button>
                    <?php } ?>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead
                        class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-900 border dark:border-gray-700 dark:text-gray-400">
                        <tr>
                            <th scope="col" class="px-4 py-3">Názov</th>
                            <th scope="col" class="px-4 py-3">Typ súboru</th>
                            <th scope="col" class="px-4 py-3">Veľkosť súboru</th>
                            <th scope="col" class="px-4 py-3">Nahral</th>
                            <th scope="col" class="px-4 py-3">Dátum</th>
                            <th scope="col" class="px-4 py-3">
                                <span class="sr-only">Actions</span>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (count($investicnyDotaznik) > 0) { ?>
                            <tr
                                class="bg-white border-b dark:bg-gray-800 transition-all dark:border-gray-700 border-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700">
                                <th scope="row"
                                    class="px-4 py-3 font-medium text-gray-900 whitespace-nowrap dark:text-white">Investičný
                                    dotazník klienta</th>
                                <td class="px-4 py-3"><span
                                        class="bg-green-100 text-green-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded-sm dark:bg-green-900 dark:text-green-300">Onboarding</span>
                                </td>
                                <td class="px-4 py-3">-</td>
                                <td class="px-4 py-3">Klient: <?php echo $clientID ?></td>
                                <td class="px-4 py-3"><?php echo $investicnyDotaznik[0]["created_at"] ?></td>
                                <td class="px-8 py-3 flex gap-3 justify-end items-center">
                                    <form id="generatePDF" method="post"
                                        action="/src/Controllers/klienti/generateDotaznikPDF.php">
                                        <input type="hidden" name="data"
                                            value='<?php echo $investicnyDotaznik[0]["data"] ?>' />
                                        <input type="hidden" name="clientID" value="<?php echo $clientID ?>" />
                                        <input type="hidden" name="name" value="<?php echo $meno ?>" />
                                        <input type="hidden" name="date"
                                            value="<?php echo $investicnyDotaznik[0]["created_at"] ?>" />
                                        <button type="submit">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-arrow-down-to-line-icon hover:dark:text-gray-100 dark:text-gray-400 transition-all text-gray-900 hover:text-gray-600 cursor-pointer lucide-arrow-down-to-line">
                                                <path d="M12 17V3" />
                                                <path d="m6 11 6 6 6-6" />
                                                <path d="M19 21H5" />
                                            </svg>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        <?php } ?>
                        <?php foreach ($dokumenty as $key => $item) { ?>
                            <tr
                                class="bg-white border-b dark:bg-gray-800 transition-all dark:border-gray-700 border-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700">
                                <th scope="row"
                                    class="px-4 py-3 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                    <section class="group flex items-center gap-4">
                                        <form class="editFileName hidden flex items-center gap-2"
                                            hx-post="/src/Controllers/klienti/detail/actions/editFileName.php"
                                            target="#toast">
                                            <input type="hidden" name="path" value="<?php echo $item["cesta"] ?>" />
                                            <input type="hidden" name="clientID" value="<?php echo $clientID ?>" />
                                            <input type="hidden" name="name" value="<?php echo $meno ?>" />
                                            <input type="hidden" name="zastupujuci_vs"
                                                value="<?php echo $zastupujuci_vs ?>" />
                                            <input type="hidden" name="oldName"
                                                value="<?php echo $item["nazov_dokument"] ?>" />
                                            <input type="text" class="py-1 px-2" name="newName"
                                                value="<?php echo $item["nazov_dokument"] ?>" />
                                            <div class="flex items-center gap-1">
                                                <button type="submit"
                                                    class="text-white hover:bg-green-700 transition-all focus:ring-4 focus:outline-none focus:ring-green-300 font-bold rounded-lg text-sm p-1.5">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                        class="lucide lucide-check-icon lucide-check">
                                                        <path d="M20 6 9 17l-5-5" />
                                                    </svg>
                                                </button>
                                                <button type="button"
                                                    class="text-white closeEdit hover:bg-gray-600 transition-all focus:ring-4 focus:outline-none focus:ring-gray-300 font-bold rounded-lg text-sm p-1.5">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                        class="lucide lucide-x-icon lucide-x">
                                                        <path d="M18 6 6 18" />
                                                        <path d="m6 6 12 12" />
                                                    </svg></button>
                                            </div>
                                        </form>
                                        <a href="<?php echo substr($item["cesta"], 16) ?>" target="_blank"
                                            class="hover:underline transition-all linkToFile">
                                            <?php echo $item["nazov_dokument"]; ?>
                                        </a>
                                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24"
                                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round"
                                            class="lucide editFileNameIcon group-hover:opacity-100 opacity-0 hover:dark:text-gray-100 dark:text-gray-400 transition-all text-gray-900 hover:text-gray-600 cursor-pointer lucide-pencil-icon lucide-pencil">
                                            <path
                                                d="M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z" />
                                            <path d="m15 5 4 4" />
                                        </svg>
                                    </section>
                                </th>
                                <td class="px-4 py-3 uppercase">
                                    <?php echo substr($item["cesta"], strpos($item["cesta"], ".") + 1); ?>
                                </td>
                                <td class="px-4 py-3">
                                    <?php echo formatBytes($item["file_size"]) ?>
                                </td>
                                <td class="px-4 py-3"><?php echo $item["username"]; ?></td>
                                <td class="px-4 py-3"><?php echo $item["datum"]; ?></td>
                                <td class="px-8 py-3 flex gap-3 justify-end items-center">
                                    <a href="<?php echo substr($item["cesta"], 16) ?>" target="_blank"
                                        class="hover:underline transition-all">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round"
                                            class="lucide hover:dark:text-gray-100 dark:text-gray-400 transition-all text-gray-900 hover:text-gray-600 cursor-pointer lucide-eye-icon lucide-eye">
                                            <path
                                                d="M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0" />
                                            <circle cx="12" cy="12" r="3" />
                                        </svg>
                                    </a>
                                    <form class="deleteDocumentForm"
                                        hx-post="/src/Controllers/klienti/detail/actions/deleteFile.php" target="#toast">
                                        <input type="hidden" name="path" value="<?php echo $item["cesta"] ?>" />
                                        <input type="hidden" name="clientID" value="<?php echo $clientID ?>" />
                                        <input type="hidden" name="name" value="<?php echo $meno ?>" />
                                        <input type="hidden" name="zastupujuci_vs" value="<?php echo $zastupujuci_vs ?>" />
                                        <button type="submit">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide hover:dark:text-red-600 dark:text-red-400 transition-all text-red-900 hover:text-red-600 cursor-pointer lucide-trash-icon lucide-trash">
                                                <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6" />
                                                <path d="M3 6h18" />
                                                <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" />
                                            </svg>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        <?php } ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</section>
<div id="upload-file-modal" tabindex="-1" aria-hidden="true"
    class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative p-4 w-full max-w-2xl max-h-full">
        <div class="relative bg-white rounded-lg shadow-sm dark:bg-gray-700">
            <div
                class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600 border-gray-200">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                    Nahrať súbor
                </h3>
                <button type="button"
                    class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                    data-modal-hide="upload-file-modal">
                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                        viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <form id="dotaznickovyForm">
                <div class="p-4 md:p-5 space-y-4">
                    <input type="hidden" name="user_id" id="user_id" value="<?php echo $clientID; ?>" />
                    <div class="flex mt-8 items-center justify-center w-full">
                        <label id="labelFileToUpload" for="fileToUpload"
                            class="flex flex-col items-center justify-center w-full h-64 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:hover:bg-bray-800 dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500 dark:hover:bg-gray-600">
                            <div class="flex flex-col items-center justify-center pt-5 pb-6">
                                <svg id="pdfIcon" class="w-8 hidden h-8 mb-4 text-gray-800 dark:text-white"
                                    aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    fill="currentColor" viewBox="0 0 24 24">
                                    <path fill-rule="evenodd"
                                        d="M9 2.221V7H4.221a2 2 0 0 1 .365-.5L8.5 2.586A2 2 0 0 1 9 2.22ZM11 2v5a2 2 0 0 1-2 2H4a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2 2 2 0 0 0 2 2h12a2 2 0 0 0 2-2 2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2V4a2 2 0 0 0-2-2h-7Zm-6 9a1 1 0 0 0-1 1v5a1 1 0 1 0 2 0v-1h.5a2.5 2.5 0 0 0 0-5H5Zm1.5 3H6v-1h.5a.5.5 0 0 1 0 1Zm4.5-3a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h1.376A2.626 2.626 0 0 0 15 15.375v-1.75A2.626 2.626 0 0 0 12.375 11H11Zm1 5v-3h.375a.626.626 0 0 1 .625.626v1.748a.625.625 0 0 1-.626.626H12Zm5-5a1 1 0 0 0-1 1v5a1 1 0 1 0 2 0v-1h1a1 1 0 1 0 0-2h-1v-1h1a1 1 0 1 0 0-2h-2Z"
                                        clip-rule="evenodd" />
                                </svg>
                                <svg id="uploadIcon" class="w-8 h-8 mb-4 text-gray-500 dark:text-gray-400"
                                    aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                                    viewBox="0 0 20 16">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2" />
                                </svg>
                                <p id="futureFile" class="mb-2 text-sm text-gray-500 dark:text-gray-400"><span
                                        class="font-semibold">Klikni
                                        pre nahratie súboru</span></p>
                                <p id="deleteText" class="text-xs text-gray-500 dark:text-gray-400">PNG, JPG alebo PDF
                                    (MAX. 1MB)</p>
                                <p id="pdfUploadError" class="text-xs text-red-500"></p>
                            </div>
                            <input id="fileToUpload" type="file" name="fileToUpload" class="hidden fileInput" />
                        </label>
                    </div>
                    <label for="expiration_date"
                        class="block mb-2 text-sm mt-6 font-medium text-gray-900 dark:text-white">Dátum expirácie (ak je aplikovateľné):</label>
                    <input type="date" id="expiration_date" name="expiration_date"
                        class="bg-gray-50 border border-gray-300 dark:bg-gray-700 dark:text-white text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5" />
                </div>
                <div class="flex items-center p-4 md:p-5 border-t border-gray-200 rounded-b dark:border-gray-600">
                    <button type="submit"
                        class="text-white bg-green-500 hover:bg-green-700 transition-all focus:ring-4 focus:outline-none focus:ring-green-300 font-bold rounded-lg text-sm px-5 py-2.5 text-center">Uložiť</button>
                    <button data-modal-hide="upload-file-modal" type="button" class="py-2.5 px-5 ms-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg 
                    border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600
                     dark:hover:text-white dark:hover:bg-gray-700">Zavrieť</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    $(".fileInput").on("change", (e) => {
        const clientID = e.currentTarget.files[0];
        console.log(clientID);
        $("#futureFile").addClass("font-bold");
        $("#pdfIcon").removeClass("hidden");
        $("#uploadIcon").addClass("hidden");
        $("#futureFile").html("Zvolený súbor: " + clientID.name);
        $("#deleteText").html("");
    });

    $("#dotaznickovyForm").on("submit", (e) => {
        e.preventDefault();
        let file = e.target.fileToUpload.files[0];
        let user_id = e.target.user_id.value;
        let expiration_date = e.target.expiration_date.value;
        let formData = new FormData();
        formData.append("fileToUpload", file);
        formData.append("user_id", user_id);
        formData.append("expiration_date", expiration_date);
        formData.append("clientID", "<?php echo $clientID ?>");
        formData.append("meno", "<?php echo $meno ?>");
        formData.append("zastupujuci_vs", "<?php echo $zastupujuci_vs ?>");

        $.ajax({
            url: `/src/Controllers/klienti/detail/actions/uploadDokumnet.php`,
            type: "POST",
            contentType: false,
            processData: false,
            dataType: "json",
            data: formData,
            success: function (data) {
                if (data.error === false) {
                    console.log(data, "DATA");
                    htmx.ajax('GET', `/api/klienti/get/documents?id=<?php echo $clientID; ?>&zastupujuci_vs=<?php echo $zastupujuci_vs; ?>&meno=<?php echo $meno ?>`, {
                        target: '#tabContentResult'
                    });
                    document.body.style.overflow = "auto";
                } else {
                    $("#pdfUploadError").html(data.errorMsg);
                    $("#labelFileToUpload").addClass("border-red-500");
                }
                console.log(data);
            }
        });
    });

    $(".editFileNameIcon").on("click", (e) => {
        $(e.currentTarget).closest("th").find(".editFileName").removeClass("hidden");
        $(e.currentTarget).closest("th").find(".editFileNameIcon").addClass("hidden");
        $(e.currentTarget).closest("th").find(".editFileName").find("input[type='text']").focus();
        $(e.currentTarget).closest("th").find(".editFileName").find("input[type='text']").select();
        $(e.currentTarget).closest("section").find(".linkToFile").hide();
    });

    $(".closeEdit").on("click", (e) => {
        $(e.currentTarget).closest("th").find(".editFileName").addClass("hidden");
        $(e.currentTarget).closest("th").find(".editFileNameIcon").removeClass("hidden");
        $(e.currentTarget).closest("section").find(".linkToFile").show();
    });
</script>