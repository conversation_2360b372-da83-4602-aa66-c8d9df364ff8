<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

function timeAgo($datetime)
{
    // Set timezone to Europe/Bratislava
    date_default_timezone_set('Europe/Bratislava');

    // Create DateTime objects for proper timezone handling
    try {
        // Parse the database datetime (assuming it's in UTC or local timezone)
        $searchTime = new DateTime($datetime);
        $currentTime = new DateTime();

        // Calculate the difference
        $interval = $currentTime->diff($searchTime);
        $time = $interval->s + ($interval->i * 60) + ($interval->h * 3600) + ($interval->d * 86400) + ($interval->m * 2592000) + ($interval->y * 31536000);

    } catch (Exception $e) {
        // Fallback to strtotime if DateTime fails
        $timestamp = strtotime($datetime);
        if ($timestamp === false) {
            return 'neznámy čas';
        }
        $time = abs(time() - $timestamp);
    }

    if ($time < 60) {
        return 'práve teraz';
    } elseif ($time < 3600) {
        $minutes = floor($time / 60);
        return $minutes . ($minutes == 1 ? ' minútu' : ($minutes < 5 ? ' minúty' : ' minút')) . ' dozadu';
    } elseif ($time < 86400) {
        $hours = floor($time / 3600);
        return $hours . ($hours == 1 ? ' hodinu' : ($hours < 5 ? ' hodiny' : ' hodín')) . ' dozadu';
    } elseif ($time < 2592000) {
        $days = floor($time / 86400);
        return $days . ($days == 1 ? ' deň' : ($days < 5 ? ' dni' : ' dní')) . ' dozadu';
    } elseif ($time < 31536000) {
        $months = floor($time / 2592000);
        return $months . ($months == 1 ? ' mesiac' : ($months < 5 ? ' mesiace' : ' mesiacov')) . ' dozadu';
    } else {
        $years = floor($time / 31536000);
        return $years . ($years == 1 ? ' rok' : ($years < 5 ? ' roky' : ' rokov')) . ' dozadu';
    }
}

$search = $_POST["query"];

if ($search === "") { ?>
    <section class="bg-white h-full dark:bg-gray-900">
        <div
            class="py-8 px-4 h-full mx-auto max-w-screen-xl flex flex-col items-center justify-center text-center lg:py-16 lg:px-12">
            <svg xmlns="http://www.w3.org/2000/svg" width="96" height="96" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-scan-search-icon dark:text-gray-100 lucide-scan-search">
                <path d="M3 7V5a2 2 0 0 1 2-2h2" />
                <path d="M17 3h2a2 2 0 0 1 2 2v2" />
                <path d="M21 17v2a2 2 0 0 1-2 2h-2" />
                <path d="M7 21H5a2 2 0 0 1-2-2v-2" />
                <circle cx="12" cy="12" r="3" />
                <path d="m16 16-1.9-1.9" />
            </svg>
            <h1
                class="mt-8 mb-4 text-4xl font-extrabold tracking-tight leading-none text-gray-900 md:text-5xl lg:text-6xl dark:text-white">
                Vyhľadávanie</h1>
            <p class="mb-8 text-lg font-normal text-gray-500 lg:text-xl sm:px-16 xl:px-48 dark:text-gray-400">Pre zobrazenie
                výsledkov napíšte do
                vyhľadávania čo chcete vyhľadať</p>
        </div>
    </section>
    <?php exit;
}

//INSERT searched term to search_log
$fts_query = preg_replace('/\s+/', ' & ', $search);
$like_query = '%' . addcslashes($search, '%_') . '%';

$sql = "SELECT type, id, title,
       ts_rank_cd(searchable, query) AS rank
FROM global_search, to_tsquery('simple', '$fts_query') query
WHERE searchable @@ query
   OR title ILIKE '$like_query'
ORDER BY rank DESC
LIMIT 20";

$results = Connection::getDataFromDatabase($sql, defaultDB)[1];
$insertSearchLog = Connection::InsertUpdateCreateDelete(
    "INSERT INTO search_log (query, user_id, search_time, result_count) VALUES (?, ?, now()::timestamp, ?)",
    [$search, $_SESSION["user"]["data"]["userid"], count($results)],
    defaultDB
);

$user_id = $_SESSION["user"]["data"]["userid"];
$recentSearches = Connection::getDataFromDatabase("SELECT query, user_id, result_count, search_time
FROM search_log s1
WHERE user_id = $user_id
AND search_time = (
    SELECT MAX(search_time)
    FROM search_log s2
    WHERE s2.query = s1.query AND s2.user_id = s1.user_id
)
ORDER BY search_time DESC
LIMIT 10;", defaultDB)[1];

$trendingSearches = Connection::getDataFromDatabase("SELECT query, COUNT(*) AS times_searched
FROM search_log
GROUP BY query
ORDER BY times_searched DESC
LIMIT 10;
", defaultDB)[1];
?>
<section id="mainSearchElement" class="p-10 h-full overflow-scroll grid grid-cols-2 gap-20">
    <section class="recentSearches relative">
        <div class="mx-auto space-y-8 sticky top-0">
            <div class="dark:bg-gray-600 dark:text-gray-100 rounded-lg p-6">
                <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center gap-3">
                        <div
                            class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h2 class="text-lg font-semibold text-gray-800 dark:text-gray-200">Naposledy vyhľadávané</h2>
                    </div>
                    <button class="text-sm text-red-600 hover:text-red-400 font-medium transition-colors">
                        Vymazať všetky
                    </button>
                </div>

                <div class="flex flex-wrap gap-4">
                    <?php foreach ($recentSearches as $key => $search) { ?>
                        <div class="group relative">
                            <div onclick="setSearchTerm(event)"
                                class="latestSearchBtn dark:bg-green-900 border dark:border-green-700 dark:hover:bg-green-700 text-white px-4 py-2 rounded-full text-sm font-medium cursor-pointer transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                                <?php echo $search["query"]; ?>
                                <button class="ml-2 opacity-70 hover:opacity-100 transition-opacity">
                                    <svg class="w-3 h-3 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </button>
                            </div>
                            <div
                                class="absolute -top-8 transform bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                                <?php echo timeAgo($search["search_time"]); ?>
                            </div>
                        </div>
                    <?php } ?>
                </div>
            </div>
            <div class="bg-white dark:bg-gray-700 rounded-lg border shadow-lg p-6">
                <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center gap-3">
                        <div
                            class="w-8 h-8 bg-gradient-to-r from-pink-500 to-rose-500 rounded-full flex items-center justify-center animate-pulse">
                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            </svg>
                        </div>
                        <h2 class="text-lg font-semibold text-gray-800 dark:text-gray-200">Najčastejšie vyľadávané</h2>
                    </div>
                    <div class="flex items-center gap-2">
                        <div class="w-2 h-2 bg-red-400 rounded-full animate-pulse"></div>
                        <span class="text-xs text-gray-500 font-medium">Live</span>
                    </div>
                </div>

                <div class="flex flex-wrap gap-3">
                    <?php foreach ($trendingSearches as $key => $search) { ?>
                        <div class="relative animate-float">
                            <div
                                class="dark:bg-gray-800 text-white px-4 py-2 rounded-full text-sm font-medium cursor-pointer transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl animate-glow">
                                <span
                                    class="bg-white/20 rounded-full px-2 py-0.5 text-xs mr-2"><?php echo $key + 1; ?></span>
                                <?php echo $search["query"]; ?>
                                <span class="ml-2 text-xs opacity-80"><?php echo $search["times_searched"]; ?>
                                    searches</span>
                            </div>
                        </div>
                    <?php } ?>
                </div>
            </div>
            <div class="bg-white dark:bg-gray-700 backdrop-blur-sm rounded-lg border border-white/50 shadow-lg p-6">
                <div class="flex items-center gap-3 mb-6">
                    <div
                        class="w-8 h-8 bg-gradient-to-r from-emerald-500 to-green-500 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <h2 class="text-lg font-semibold text-gray-800">Rýchle akcie</h2>
                </div>

                <div class="flex flex-wrap gap-3">
                    <div hx-get="/klienti" hx-target="#pageContentMain" hx-replace-url="true" hx-push-url="true"
                        class="bg-gray-100 hover:bg-green-700 quickActionBtn text-gray-700 hover:text-white px-4 py-2 rounded-full text-sm font-medium cursor-pointer transition-all duration-300 transform hover:scale-105 shadow-sm hover:shadow-lg">
                        Klienti
                    </div>

                    <div hx-get="/aktiva/cenne-papiere" hx-target="#pageContentMain" hx-replace-url="true"
                        hx-push-url="true"
                        class="bg-gray-100 hover:bg-green-700 quickActionBtn text-gray-700 hover:text-white px-4 py-2 rounded-full text-sm font-medium cursor-pointer transition-all duration-300 transform hover:scale-105 shadow-sm hover:shadow-lg">
                        Cenné papiere
                    </div>

                    <div hx-get="/uzavierka" hx-target="#pageContentMain" hx-replace-url="true" hx-push-url="true"
                        class="bg-gray-100 hover:bg-green-700 quickActionBtn text-gray-700 hover:text-white px-4 py-2 rounded-full text-sm font-medium cursor-pointer transition-all duration-300 transform hover:scale-105 shadow-sm hover:shadow-lg">
                        Uzávierka
                    </div>

                    <div hx-get="/klienti/create-new" hx-target="#pageContentMain" hx-replace-url="true"
                        hx-push-url="true"
                        class="bg-gray-100 hover:bg-green-700 quickActionBtn text-gray-700 hover:text-white px-4 py-2 rounded-full text-sm font-medium cursor-pointer transition-all duration-300 transform hover:scale-105 shadow-sm hover:shadow-lg">
                        + Pridať klienta
                    </div>
                    <?php if ($_SESSION["mode"]["mode"] === "global") { ?>
                        <div hx-get="/investicne-zamery" hx-target="#pageContentMain" hx-replace-url="true"
                            hx-push-url="true"
                            class="bg-gray-100 hover:bg-green-700 quickActionBtn text-gray-700 hover:text-white px-4 py-2 rounded-full text-sm font-medium cursor-pointer transition-all duration-300 transform hover:scale-105 shadow-sm hover:shadow-lg">
                            + Nový investičný zámer (global)
                        </div>
                    <?php } ?>
                </div>
            </div>
        </div>
    </section>
    <section class="results">
        <?php foreach ($results as $key => $item) {
            if ($item["type"] === "podielnik") {
                include "clientCard.php";
            }
        } ?>
    </section>
</section>