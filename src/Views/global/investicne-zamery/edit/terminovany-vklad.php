<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
$dealID = isset($matches[1]) ? intval($matches[1]) : null;


if (isset($_SESSION["client"])) {
    $fondid = $_SESSION["client"]["fondid"];
} else {
    $fondid = 0;
}

$detail = Connection::getDataFromDatabase("SELECT k.*,
       d.cislo as cislo
from konfirmaciaktv k,
     dennikpm d
where k.logactivityid in (4, 6, 7, 8, 9)
  and k.dealid = $dealID
  and k.dealid = d.dealid
  and k.SUBJEKTID = $fondid", defaultDB)[1][0];

if (str_contains($_SERVER['REQUEST_URI'], "terminovany-vklad")) {
    $object = "KTV";
    $color = "#fdba74";
}

$now = strtotime($detail["k_td"]);
$your_date = strtotime($detail["z_td"]);
$datediff = $now - $your_date;

$userID = $detail["loguserid"];
$user = Connection::getDataFromDatabase("SELECT userid, username from users WHERE userid = $userID", defaultDB)[1][0];

$partnerid = $detail["partnerid"];
$partnerName = Connection::getDataFromDatabase("SELECT nazovpartnera FROM partner WHERE partnerid = $partnerid", defaultDB)[1][0]["nazovpartnera"];
?>
<section class="relative mt-3 px-5">
    <form id="updateZamer" hx-post="/api/investicne-zamery/terminovany-vklad/update/<?php echo $object; ?>"
        hx-target="#toast">
        <input type="hidden" name="dealid" id="dealid" value="<?php echo $dealID; ?>" />
        <input type="hidden" name="dealidseq" id="dealidseq" value="<?php echo $detail["dealidseq"]; ?>" />
        <div class="flex justify-between pb-2 mb-4 items-center border-b">
            <h2 class="scroll-m-20 text-3xl font-semibold tracking-tight first:mt-0">
                Investičný zámer <span style="background: <?php echo $color; ?>;"
                    class="text-gray-900 font-bold px-2 py-0.5 rounded-lg"><?php echo $object ?></span>
                <span style="background: #78716c;"
                    class="text-white font-bold px-2 py-0.5 rounded-lg"><?php echo $dealID ?></span>
            </h2>
            <div class="flex items-center gap-1">
                Autor:
                <a href="/pouzivatelia/detail/<?php echo $user["userid"]; ?>"
                    class="flex items-center px-3 gap-2 p-0.5 rounded-lg hover:bg-gray-200 hover:underline cursor-pointer transition-all">

                    <div
                        class="relative w-5 h-5 overflow-hidden bg-gray-200 flex items-center justify-center rounded-full dark:bg-gray-600">
                        <svg class="w-5 h-5 text-gray-400 -mb-2" fill="currentColor" viewBox="0 0 20 20"
                            xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                                clip-rule="evenodd">
                            </path>
                        </svg>
                    </div>
                    <small><?php echo $user["username"] ?></small>
                </a>
            </div>
            <button type="submit"
                class="focus:outline-none flex items-center transition-all gap-2 text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:ring-green-300 rounded-lg text-md font-extrabold px-5 py-2.5 dark:bg-green-600 dark:hover:bg-green-700 dark:focus:ring-green-800">Aktualizovať
                <svg id="updateChecks" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-check-check">
                    <path d="M18 6 7 17l-5-5" />
                    <path d="m22 10-7.5 7.5L13 16" />
                </svg>
                <svg id="updateSpinner" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide hidden animate-spin lucide-loader-circle">
                    <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                </svg>
            </button>
        </div>
        <div class="flex items-center justify-between p-3 mb-8 bg-white shadow-md rounded-lg">
            <p class="text-xs">Zvolená mena: <span class="text-lg font-bold"><?php echo $detail["mena"]; ?></span></p>
            <p class="text-xs">Zvolený partner: <span class="text-lg font-bold"><?php echo $partnerName; ?></span></p>
            <p class="text-xs">Zvolený účet: <span class="text-lg font-bold"><?php echo $detail["cub"]; ?></span></p>
        </div>
        <input type="hidden" name="partnerid" id="partnerid" value="<?php echo $detail["partnerid"]; ?>" />
        <input type="hidden" name="mena" id="mena" value="<?php echo $detail["mena"] ?>" />
        <input type="hidden" name="ucet" id="ucet" value="<?php echo $detail["cub"] ?>" />
        <div class="grid grid-cols-2 gap-4">
            <section>
                <div class="mb-2">
                    <label for="datekonf" class="block mb-2 text-sm font-bold text-gray-900 dark:text-white">Dátum
                        konfirmácie</label>
                    <input type="date" id="datekonf" name="datekonf" min="<?php echo date('Y-m-d'); ?>"
                        value="<?php echo $detail["dk_td"]; ?>"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                        required />
                </div>
                <div class="mb-2">
                    <label for="datestart" class="block mb-2 text-sm font-bold text-gray-900 dark:text-white">Dátum
                        začiatku</label>
                    <input type="date" id="datestart" name="datestart" min="<?php echo date('Y-m-d'); ?>"
                        value="<?php echo $detail["z_td"]; ?>"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                        required />
                </div>
                <div class="mb-2">
                    <label for="datesplatnost" class="block mb-2 text-sm font-bold text-gray-900 dark:text-white">Dátum
                        splatnosti</label>
                    <input type="date" id="datesplatnost" name="datesplatnost" min="<?php echo date('Y-m-d'); ?>"
                        value="<?php echo $detail["k_td"]; ?>"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                        required />
                </div>
                <div class="mb-2">
                    <label for="dobaviazanosti" class="block mb-2 text-sm font-bold text-gray-900 dark:text-white">Doba
                        viazanosti</label>
                    <input type="hidden" name="dobaviazanosti" id="dobaviazanosti"
                        value="<?php echo round($datediff / (60 * 60 * 24)); ?>" />
                    <p id="dobaviazanostiText">
                        <?php echo round($datediff / (60 * 60 * 24));
                        echo " dní  [" . date('l', strtotime($detail["dk_td"])) . "]"; ?>
                    </p>
                </div>
                <div class="mb-2">
                    <label for="sadzba"
                        class="block mb-2 text-sm font-bold text-gray-900 dark:text-white">Sadzba</label>
                    <input type="text" id="sadzba" name="sadzba" value="<?php echo $detail["ir_td"]; ?>"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                        required />
                </div>
                <section class="grid grid-cols-1 py-4 px-2 md:grid-cols-2 gap-6">
                    <div class="p-3 rounded-lg shadow-md transition-all flex flex-col justify-center items-center"
                        style="background-color: <?php echo $color ?>;">
                        <label for="istina"
                            class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Istina</label>
                        <input type="hidden" name="istina" id="istinaValue" value="<?php echo $detail["sum_td"]; ?>" />
                        <p class="font-bold text-lg" id="istina">
                            <?php echo number_format($detail["sum_td"], 2, ".", " ") . " " . $detail["mena"]; ?>
                        </p>
                    </div>
                    <div class="p-3 rounded-lg shadow-md transition-all flex flex-col justify-center items-center"
                        style="background-color: <?php echo $color ?>;">
                        <label for="urokbrutto"
                            class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Úrok
                            brutto</label>
                        <input type="hidden" name="urokbrutto" id="urokbrutto" value="<?php echo $detail["iv_b"]; ?>" />
                        <p class="font-bold text-lg" id="urokbruttoText"><?php echo $detail["iv_b"]; ?></p>
                    </div>
                    <div class="p-3 rounded-lg shadow-md transition-all flex flex-col justify-center items-center"
                        style="background-color: <?php echo $color ?>;">
                        <label for="uroknetto" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Úrok
                            netto</label>
                        <input type="hidden" name="uroknetto" id="uroknetto" value="<?php echo $detail["iv_n"]; ?>" />
                        <p class="font-bold text-lg" id="uroknettoText"><?php echo $detail["iv_n"]; ?></p>
                    </div>
                    <div class="p-3 rounded-lg shadow-md transition-all flex flex-col justify-center items-center"
                        style="background-color: <?php echo $color ?>;">
                        <label for="istinanetto"
                            class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Istina +
                            úrok netto</label>
                        <input type="hidden" name="istinanetto" id="istinanetto" />
                        <p class="font-bold text-lg" id="istinanettoText">
                            <?php echo number_format($detail["iv_n"] + $detail["sum_td"], 2, ".", " ") . " " . $detail["mena"]; ?>
                        </p>
                    </div>
                </section>
            </section>
            <section>
                <div class="mb-2">
                    <label for="dan" class="block mb-2 text-sm font-bold text-gray-900 dark:text-white">Daň</label>
                    <input type="text" id="dan" name="dan" value="<?php echo $detail["dan"]; ?>"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                        required />
                </div>
                <div class="mb-2">
                    <label for="urocenie"
                        class="block mb-2 text-sm font-bold text-gray-900 dark:text-white">Úročenie</label>
                    <select name="urocenie" id="urocenie"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                        tabindex="7">
                        <option value="360" <?php echo ($detail["cond1"] === "360") ? "selected" : "" ?>>360</option>
                        <option value="365" <?php echo ($detail["cond1"] === "365") ? "selected" : "" ?>>365</option>
                        <option value="366" <?php echo ($detail["cond1"] === "366") ? "selected" : "" ?>>366</option>
                    </select>
                </div>
                <div class="mb-2">
                    <label for="autoprolognacia"
                        class="block mb-2 text-sm font-bold text-gray-900 dark:text-white">Autoprolongácia</label>
                    <select
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                        name="autoprolognacia" tabindex="9">
                        <option value="1" <?php echo ($detail["cond3"] === "'t'") ? "selected" : "" ?>>Áno</option>
                        <option value="0" <?php echo ($detail["cond3"] === "'f'") ? "selected" : "" ?>>Nie</option>
                    </select>
                </div>
                <div class="mb-2">
                    <label for="kapitalizovanie"
                        class="block mb-2 text-sm font-bold text-gray-900 dark:text-white">Kapitalizovanie</label>
                    <select
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                        name="kapitalizovanie" tabindex="9">
                        <option value="1" <?php echo ($detail["cond4"] === "'t'") ? "selected" : "" ?>>Áno</option>
                        <option value="0" <?php echo ($detail["cond4"] === "'f'") ? "selected" : "" ?>>Nie</option>
                    </select>
                </div>
                <div class="mb-2">
                    <label for="predcasnyvyber"
                        class="block mb-2 text-sm font-bold text-gray-900 dark:text-white">Predčasný
                        výber</label>
                    <select
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                        name="predcasnyvyber" tabindex="9">
                        <option value="1" <?php echo ($detail["cond5"] === "'t'") ? "selected" : "" ?>>Áno</option>
                        <option value="0" <?php echo ($detail["cond5"] === "'f'") ? "selected" : "" ?>>Nie</option>
                    </select>
                </div>
                <div class="mb-2">
                    <label for="sankcia"
                        class="block mb-2 text-sm font-bold text-gray-900 dark:text-white">Sankcia</label>
                    <select
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                        name="sankcia" tabindex="9">
                        <option value="1" <?php echo ($detail["cond6"] === "'t'") ? "selected" : "" ?>>Áno</option>
                        <option value="0" <?php echo ($detail["cond6"] === "'f'") ? "selected" : "" ?>>Nie</option>
                    </select>
                </div>
                <div class="mb-2">
                    <label for="prevodistiny" class="block mb-2 text-sm font-bold text-gray-900 dark:text-white">Prevod
                        istiny</label>
                    <input type="hidden" name="prevodistiny" id="prevodisitinyinput"
                        value="<?php echo $detail["cub"]; ?>" />
                    <p id="prevodistiny"><?php echo $detail["cub"]; ?></p>
                </div>
                <div class="mb-2">
                    <label for="prevoduroku" class="block mb-2 text-sm font-bold text-gray-900 dark:text-white">Prevod
                        úroku</label>
                    <input type="hidden" name="prevoduroku" id="prevodisitinyinput"
                        value="<?php echo $detail["cub"]; ?>" />
                    <p id="prevoduroku"><?php echo $detail["cub"]; ?></p>
                </div>
            </section>
        </div>
    </form>
</section>
<div id="toast" class="opacity-0 hidden absolute right-5 bottom-1">

</div>

<script>
    document.getElementById("datesplatnost").addEventListener("change", (e) => {
        let startDate = document.getElementById("datestart").value;
        let endDate = e.target.value;
        const diffInMs = new Date(endDate) - new Date(startDate);
        const diffInDays = diffInMs / (1000 * 60 * 60 * 24);
        const day = new Date(endDate).getDay();
        let den = "";
        switch (day) {
            case 1:
                den = "Pondelok";
                break;
            case 2:
                den = "Utorok";
                break;
            case 3:
                den = "Streda";
                break;
            case 4:
                den = "Štvrtok";
                break;
            case 5:
                den = "Piatok";
                break;
            case 6:
                den = "Sobota";
                break;
            case 0:
                den = "Nedeľa";
                break;
        }
        if (diffInDays !== NaN) {
            document.getElementById("dobaviazanosti").value = diffInDays;
            document.getElementById("dobaviazanostiText").innerHTML = diffInDays + " dní. Deň: " + den;
        } else {
            document.getElementById("dobaviazanosti").value = 0;
        }
    });

    function recalculateTVValues() {
        let metoda = document.getElementById("urocenie").value;
        let sadzba = document.getElementById("sadzba").value;
        let pocetdni = document.getElementById("dobaviazanosti").value;
        let istina = document.getElementById("istinaValue").value;
        let brutto = (Math.round((((parseFloat(sadzba) / (100 * parseInt(metoda))) * parseInt(pocetdni)) * 500000) * 100)) / 100;

        document.getElementById("urokbrutto").value = brutto;
        document.getElementById("urokbruttoText").innerHTML = brutto;

        let dan = document.getElementById("dan").value;
        let danPokus1 = (Math.floor(parseInt(brutto) * parseInt(dan))) / 100;
        let netto = brutto - danPokus1;

        document.getElementById("uroknetto").value = netto.toFixed(2);
        document.getElementById("uroknettoText").innerHTML = netto.toFixed(2);

        document.getElementById("istinanetto").value = (netto + parseInt(istina)).toFixed(2).toLocaleString();
        document.getElementById("istinanettoText").innerHTML = (netto + parseInt(istina)).toFixed(2).toLocaleString();
    }

    document.getElementById("sadzba").addEventListener("keyup", (e) => {
        recalculateTVValues();
    });

    document.getElementById("datesplatnost").addEventListener("change", (e) => {
        recalculateTVValues();
    });

    document.addEventListener('htmx:afterSettle', function (evt) {
        document.getElementById("updateChecks").style.display = "block";
        document.getElementById("updateSpinner").style.display = "none";
        document.getElementById("toast").style.display = "flex";
        $(document.getElementById("toast")).animate({ opacity: 1 }, 500);
        setTimeout(() => {
            $(document.getElementById("toast")).animate({ opacity: 0 }, 500);
        }, 3000);
    });



    document.getElementById("updateZamer").addEventListener("submit", (e) => {
        document.getElementById("updateChecks").style.display = "none";
        document.getElementById("updateSpinner").style.display = "block";
    });
</script>