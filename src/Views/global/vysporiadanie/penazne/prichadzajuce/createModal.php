<?php require_once('/home/<USER>/www/src/lib/connection.php');
require_once("/home/<USER>/www/conf/settings.php");
$zmluvy = Connection::getDataFromDatabase("select distinct * from portfolio", defaultDB)[1];
$menyQuery = "SELECT DISTINCT
       pr.mena
FROM portfolio po
         JOIN poplatok_register pr ON po.fondid = pr.fondid
         JOIN fonds f ON f.fondid = po.fondid
         LEFT JOIN konfirmaciacp kcp ON pr.dealid = kcp.dealid
WHERE pr.stav IN (0,1,5)
  AND pr.datum IS NOT NULL AND pr.suma IS NOT NULL";
$meny = Connection::getDataFromDatabase(
    $menyQuery,
    defaultDB
)[1]; ?>
<div id="create-modal" tabindex="-1" aria-hidden="true" class="hidden overflow-y-auto bg-clip-padding backdrop-filter backdrop-blur-sm bg-opacity-10 dark:bg-gray-400/30 bg-white/60 overflow-x-hidden 
fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 max-h-full flex">
    <div id="toastCreate" class="absolute w-full top-5"></div>
    <div class="relative p-4 w-full max-w-2xl max-h-full">
        <div class="relative bg-white rounded-lg shadow-sm dark:bg-gray-700">
            <div
                class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600 border-gray-200">
                <h3 class="text-xl flex items-center gap-2 font-semibold text-gray-900 dark:text-white">
                    Pridať úhradu pre účet <strong><?php echo $cub; ?></strong>
                    <span
                        class="bg-gray-100 text-gray-800 text-sm font-bold me-2 px-2.5 py-0.5 rounded-sm dark:bg-gray-700 dark:text-gray-300"><?php echo $mena; ?></span>
                </h3>
                <button type="button"
                    class="text-gray-400 bg-transparent hover:bg-gray-50 dark:hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                    data-modal-hide="create-modal">
                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                        viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <form class="mb-0 executeActionUhradaForm">
                <input type="hidden" id="action" name="action" value="create" />
                <input type="hidden" id="menaInput" name="mena" value="<?php echo $mena; ?>" />
                <input type="hidden" id="cub" name="cub" value="<?php echo $cub; ?>" />
                <div class="p-4 md:p-5 grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="suma"
                            class="block text-sm font-medium dark:text-gray-100 text-gray-700 mb-1">Suma</label>
                        <input type="number" id="suma" name="suma" min="0" step="0.01" required
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full 
                            p-1.5 dark:bg-gray-600 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" placeholder="0.00">
                    </div>
                    <div>
                        <label for="forma"
                            class="block text-sm font-medium dark:text-gray-100 text-gray-700 mb-1">Forma</label>
                        <div class="flex w-full items-center">
                            <select id="forma" name="forma" required
                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full 
                            p-1.5 dark:bg-gray-600 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                <option value="0" selected>Prevod</option>
                                <option value="1">Hotovosť</option>
                            </select>
                        </div>
                    </div>
                    <div>
                        <label for="ucetpartnera"
                            class="block text-sm font-medium dark:text-gray-100 text-gray-700 mb-1">Účet
                            partnera</label>
                        <input type="text" id="ucetpartnera" name="ucetpartnera" required
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full 
                            p-1.5 dark:bg-gray-600 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    </div>
                    <div>
                        <label for="nazpartnera" class="block text-sm font-medium text-gray-700 dark:text-gray-100 mb-1">Názov
                            partnera</label>
                        <input type="text" id="nazpartnera" name="nazpartnera" required
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full 
                            p-1.5 dark:bg-gray-600 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    </div>
                    <div>
                        <label for="ks" class="block text-sm font-medium text-gray-700 dark:text-gray-100 mb-1">Konštatný symbol</label>
                        <input type="text" id="ks" name="ks" required minlength="3"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full 
                            p-1.5 dark:bg-gray-600 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    </div>
                    <div>
                        <label for="vs" class="block text-sm font-medium text-gray-700 dark:text-gray-100 mb-1">Variabilný symbol</label>
                        <input type="text" id="vs" name="vs" required minlength="3"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full 
                            p-1.5 dark:bg-gray-600 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    </div>
                    <div>
                        <label for="ss" class="block text-sm font-medium text-gray-700 dark:text-gray-100 mb-1">Špecifický symbol</label>
                        <input type="text" id="ss" name="ss"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full 
                            p-1.5 dark:bg-gray-600 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    </div>
                    <div>
                        <label for="date" class="block text-sm font-medium text-gray-700 dark:text-gray-100 mb-1">Dátum a čas</label>
                        <input type="date" id="date" name="date" required
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full 
                            p-1.5 dark:bg-gray-600 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    </div>
                </div>
                <div
                    class="flex items-center gap-2 justify-end p-4 md:p-5 border-t border-gray-200 rounded-b dark:border-gray-600 bg-gray-900 sticky bottom-1">
                    <button data-modal-hide="create-modal" type="button"
                        class="py-2.5 px-5 ms-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">Zrušiť</button>
                    <button type="submit"
                        class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5
                         py-2.5 text-center flex gap-2 items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                        <span id="createFeeSubmit">Potvrdiť</span>
                        <svg id="createFeeSubmitSpinner" xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                            stroke-linecap="round" stroke-linejoin="round"
                            class="lucide hidden animate-spin lucide-loader-circle">
                            <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                        </svg>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>