<?php $data = json_decode($_POST["data"]);?>
<input type="hidden" id="action" name="action" value="update" />
<input type="hidden" id="menaInput" name="mena" value="<?php echo $data->mena; ?>" />
<input type="hidden" id="cub" name="cub" value="<?php echo $data->cub; ?>" />
<input type="hidden" id="id" name="id" value="<?php echo $data->id; ?>" />
<div class="p-4 md:p-5 grid grid-cols-1 md:grid-cols-2 gap-6">
    <div>
        <label for="suma" class="block text-sm font-medium dark:text-gray-100 text-gray-700 mb-1">Suma</label>
        <input type="number" id="suma" name="suma" min="0" step="0.01" required value="<?php echo $data->suma; ?>"
            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full
                            p-1.5 dark:bg-gray-600 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
    </div>
    <div>
        <label for="forma" class="block text-sm font-medium dark:text-gray-100 text-gray-700 mb-1">Forma</label>
        <div class="flex w-full items-center">
            <select id="forma" name="forma" required
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full
                            p-1.5 dark:bg-gray-600 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                <option value="0" <?php echo $data->forma === 0 ? "selected" : "" ?>>Prevod</option>
                <option value="1" <?php echo $data->forma === 1 ? "selected" : "" ?>>Hotovosť</option>
            </select>
        </div>
    </div>
    <div>
        <label for="ucetpartnera" class="block text-sm font-medium dark:text-gray-100 text-gray-700 mb-1">Účet
            partnera</label>
        <input type="text" id="ucetpartnera" name="ucetpartnera" required value="<?php echo $data->ucetpartnera; ?>"
            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full
                            p-1.5 dark:bg-gray-600 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
    </div>
    <div>
        <label for="nazpartnera" class="block text-sm font-medium text-gray-700 dark:text-gray-100 mb-1">Názov partnera</label>
        <input type="text" id="nazpartnera" name="nazpartnera" required value="<?php echo $data->nazovpartnera; ?>"
            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full
                            p-1.5 dark:bg-gray-600 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
    </div>
    <div>
        <label for="ks" class="block text-sm font-medium text-gray-700 dark:text-gray-100 mb-1">Konštatný symbol</label>
        <input type="text" id="ks" name="ks" required minlength="3" value="<?php echo $data->ks; ?>"
            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full
                            p-1.5 dark:bg-gray-600 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
    </div>
    <div>
        <label for="vs" class="block text-sm font-medium text-gray-700 dark:text-gray-100 mb-1">Variabilný symbol</label>
        <input type="text" id="vs" name="vs" required minlength="3" value="<?php echo $data->vs; ?>"
            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full
                            p-1.5 dark:bg-gray-600 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
    </div>
    <div>
        <label for="ss" class="block text-sm font-medium text-gray-700 dark:text-gray-100 mb-1">Špecifický symbol</label>
        <input type="text" id="ss" name="ss" required value="<?php echo $data->ss; ?>"
            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full
                            p-1.5 dark:bg-gray-600 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
    </div>
    <div>
        <label for="date" class="block text-sm font-medium text-gray-700 dark:text-gray-100 mb-1">Dátum a čas</label>
        <input type="date" id="date" name="date" required value="<?php echo $data->datum; ?>"
            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full
                            p-1.5 dark:bg-gray-600 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
    </div>
</div>