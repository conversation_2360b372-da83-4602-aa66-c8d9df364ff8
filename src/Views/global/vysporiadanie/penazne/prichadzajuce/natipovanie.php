<?php
require_once('/home/<USER>/www/src/lib/connection.php');
require_once("/home/<USER>/www/conf/settings.php");
require_once("/home/<USER>/www/src/Components/exports/excel/excelController.php");

$cub = $_GET["ucet"];
$mena = $_GET["mena"];

if (isset($_SESSION["client"])) {
    $id_fond = $_SESSION["client"]["fondid"];
} else if ($_SESSION["mode"]["mode"] === "admin") {
    $id_fond = 1;
} else {
    $id_fond = 0;
}

$query = "SELECT obratdatetime as obratdt, b.*, p.cislozmluvy, a.activitypopis
from obratybu b
         LEFT JOIN portfolio p ON p.fondid = b.subjektid
         JOIN activity a ON a.activityid = b.logactivityid AND a.destinacia = 'obratybu'
where b.logactivityid in (1, 2)
  and b.cub like '$cub'
  and b.subjektid = $id_fond
  and krdb = 1
GROUP BY b.subjektid, obratdatetime, p.cislozmluvy, b.id, b.cub, b.mena, b.suma, b.vs, b.ss, b.ks, b.forma,
         b.cubpartnera, b.nazpartnera, b.logactivityid, b.loguserid, b.logdatatimeactivity, krdb, a.activitypopis";
echo $query;
$uhrady = Connection::getDataFromDatabase($query, defaultDB)[1];
$columns = ["Klient", "Forma", "Názov", "Účet partnera", "Suma", "Dátum", "Status"];

?>
<section class="py-4 relative px-5 sm:py-5" style="margin-bottom: 8rem">
    <div id="toast" class="z-30 top-26 fixed right-5"></div>
    <input type="hidden" id="pageNumber" name="page" value="<?php echo $page ?>" />
    <div class="relative w-full shadow-md sm:rounded-lg">
        <div id="tableTopHeader" class="p-4 bg-white z-10 flex justify-between dark:bg-gray-900">
            <button type="submit" id="wholeBodySubmitter" class="hidden h-0 w-0"></button>
            <div class="relative flex items-center gap-4 mt-1">
                <h1
                    class="mb-4 text-3xl font-extrabold leading-tight text-gray-900 lg:mb-6 lg:text-3xl dark:text-white">
                    Zoznam
                    úhrad pre účet <strong id="totalCountStrong"
                        class="text-green-400 dark:text-green-600"><?php echo $cub; ?></strong>
                </h1>
            </div>
            <section class="flex items-center gap-4">
                <button type="button" data-modal-target="create-modal" data-modal-toggle="create-modal"
                    class="text-white bg-gradient-to-r cursor-pointer transition-all from-green-400 via-green-500 to-green-600 hover:bg-gradient-to-br
                    focus:ring-4 focus:outline-none focus:ring-green-300 dark:focus:ring-green-800 shadow-lg shadow-green-500/50 dark:shadow-lg 
                    dark:shadow-green-800/80 font-bold flex items-center gap-2 rounded-lg text-sm px-5 py-2.5 text-center">
                    Pridať
                    úhradu
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-list-plus">
                        <path d="M11 12H3" />
                        <path d="M16 6H3" />
                        <path d="M16 18H3" />
                        <path d="M18 9v6" />
                        <path d="M21 12h-6" />
                    </svg>
                </button>
            </section>
        </div>
        <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
            <thead class="text-xs text-gray-700 bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                <tr>
                    <th scope="col" class="px-6 py-3 relative">
                        <div class="flex items-center gap-1 p-1 px-3">
                            <span class="text-xs uppercase mr-2">Forma</span>
                        </div>
                    </th>
                    <th scope="col" class="px-6 py-3 relative">
                        <div class="flex items-center gap-1 p-1 px-3">
                            <span class="text-xs uppercase mr-2">Názov</span>
                        </div>
                    </th>
                    <th scope="col" class="px-6 py-3 relative">
                        <div class="flex items-center gap-1 p-1 px-3">
                            <span class="text-xs uppercase mr-2">Účet partnera</span>
                        </div>
                    </th>
                    <th scope="col" class="px-6 py-3">
                        <div class="flex items-center gap-1 p-1 px-3">
                            <span class="text-xs uppercase mr-2">Suma</span>
                        </div>
                    </th>
                    <th scope="col" class="px-1 py-3">
                        <div class="flex items-center gap-1 p-1 px-3">
                            <span class="text-xs uppercase mr-2">Dátum</span>
                        </div>
                    </th>
                    <th scope="col" class="px-1 py-3">
                        <div class="flex items-center gap-1 p-1 px-3">
                            <span class="text-xs uppercase mr-2">Status</span>
                        </div>
                    </th>
                    <th scope="col" class="px-1 uppercase py-3">
                        Akcie
                    </th>
                </tr>
            </thead>
            <tbody id="poplatkyTBODY">
                <?php
                foreach ($uhrady as $key => $uhrada) { ?>
                    <tr
                        class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600">
                        <td class="px-6 py-4">
                            <span
                                class="flex w-full justify-between items-center"><?php echo $uhrada["forma"] == "0" ? "Prevod" : "Hotovosť"; ?></span>
                        </td>
                        <td class="px-6 py-4 reconfirmedColumnData">
                            <span class="flex w-full justify-between items-center"><small class="font-bold">
                                    <?php echo $uhrada["nazpartnera"] === NULL ? "-" : $uhrada["nazpartnera"]; ?></small></span>
                        </td>
                        <td class="px-6 py-4">
                            <?php echo $uhrada["cub"]; ?>
                        </td>
                        <td class="px-6 py-4">
                            <?php echo $uhrada["suma"]; ?>
                        </td>
                        <td class="px-6 menaColumnData py-4">
                            <?php echo $uhrada["obratdt"]; ?>
                        </td>
                        <td class="px-6 menaColumnData py-4">
                            <span
                                class="bg-indigo-100 text-indigo-800 text-sm font-bold me-2 px-2.5 py-0.5 text-xs rounded-sm dark:bg-indigo-900 dark:text-indigo-300">
                                <?php echo $uhrada["activitypopis"]; ?></span>
                        </td>
                        <td class="px-6 py-4 relative">
                            <form class="mb-0 uhradaForm">
                                <input type="hidden" id="forma" name="forma" value="<?php echo $uhrada["forma"]; ?>" />
                                <input type="hidden" id="nazovpartnera" name="nazovpartnera"
                                    value="<?php echo $uhrada["nazpartnera"]; ?>" />
                                <input type="hidden" id="ucetpartnera" name="ucetpartnera"
                                    value="<?php echo $uhrada["cub"]; ?>" />
                                <input type="hidden" id="datum" name="datum" value="<?php echo $uhrada["obratdt"]; ?>" />
                                <input type="hidden" id="suma" name="suma" value="<?php echo $uhrada["suma"]; ?>" />
                                <input type="hidden" id="vs" name="vs" value="<?php echo $uhrada["vs"]; ?>" />
                                <input type="hidden" id="ss" name="ss" value="<?php echo $uhrada["ss"]; ?>" />
                                <input type="hidden" id="ks" name="ks" value="<?php echo $uhrada["ks"]; ?>" />
                                <input type="hidden" id="id" name="id" value="<?php echo $uhrada["id"]; ?>" />
                                <input type="hidden" id="kodobratu" name="kodobratu"
                                    value="<?php echo $uhrada["kodobratu"]; ?>" />
                                <div class="inline-flex gap-2">
                                    <button type="submit" id="edit" class="p-2 text-sm rounded-lg font-medium text-gray-900 bg-white hover:bg-gray-200 transition-all hover:text-blue-700 focus:z-10 focus:ring-2 focus:ring-blue-700
                                        focus:text-blue-700 dark:bg-gray-800 dark:text-white dark:hover:text-white dark:hover:bg-gray-700
                                        dark:focus:ring-blue-500 dark:focus:text-white" data-modal-target="editModal"
                                        data-modal-toggle="editModal">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round" class="lucide lucide-pencil">
                                            <path
                                                d="M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z" />
                                            <path d="m15 5 4 4" />
                                        </svg>
                                    </button>
                                    <button type="submit" id="storno" class="p-2 text-sm rounded-lg font-medium text-red-900 bg-white hover:bg-gray-200 transition-all hover:text-red-700 focus:z-10 focus:ring-2 focus:ring-red-700
                                        focus:text-red-700 dark:bg-gray-800 dark:text-white dark:hover:text-white dark:hover:bg-gray-700
                                        dark:focus:ring-red-500 dark:focus:text-white"
                                        data-modal-target="sureModal<?php echo $key; ?>"
                                        data-modal-toggle="sureModal<?php echo $key; ?>">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round" class="lucide lucide-circle-x">
                                            <circle cx="12" cy="12" r="10" />
                                            <path d="m15 9-6 6" />
                                            <path d="m9 9 6 6" />
                                        </svg>
                                    </button>
                                    <button type="submit" id="zapocet"
                                        class="p-2 text-sm font-medium text-gray-900 bg-white rounded-lg hover:bg-gray-200 transition-all hover:text-blue-700 focus:z-10 focus:ring-2 focus:ring-blue-700 focus:text-blue-700 dark:bg-gray-800 dark:text-white dark:hover:text-white dark:hover:bg-gray-700 dark:focus:ring-blue-500 dark:focus:text-white">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round" class="lucide zapocetIcon lucide-send">
                                            <path
                                                d="M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z" />
                                            <path d="m21.854 2.147-10.94 10.939" />
                                        </svg>
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                            style="display: none;" stroke-linejoin="round"
                                            class="lucide zapocetSpin animate-spin lucide-loader-circle">
                                            <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                                        </svg>
                                    </button>
                                </div>
                            </form>
                        </td>
                    </tr>
                <?php } ?>
            </tbody>
        </table>
    </div>
</section>
<?php include "createModal.php"; ?>
<?php include "editModal.php"; ?>
<?php foreach ($uhrady as $key => $uhrada) { ?>
    <?php include "sureModal.php"; ?>
<?php } ?>
<script>
    document.querySelectorAll(".uhradaForm").forEach((item) => {
        item.addEventListener("submit", (e) => {
            e.preventDefault();
            const submitter = e.submitter.id;
            const formData = new FormData(e.currentTarget);
            console.log(submitter);
            switch (submitter) {
                case "edit":
                    htmx.ajax('POST', `/src/Views/global/vysporiadanie/penazne/prichadzajuce/editUhradaForm.php`, {
                        target: "#editUhradaDPC",
                        values: {
                            "data": JSON.stringify({
                                suma: formData.get("suma"),
                                forma: formData.get("forma"),
                                id: formData.get("id"),
                                ucetpartnera: formData.get("ucetpartnera"),
                                nazovpartnera: formData.get("nazovpartnera"),
                                datum: formData.get("datum"),
                                vs: formData.get("vs"),
                                ss: formData.get("ss"),
                                ks: formData.get("ks"),
                                cub: '<?php echo $cub; ?>',
                                mena: '<?php echo $mena; ?>'
                            })
                        }
                    }).then((response) => {
                    });
                    break;
                case "zapocet":
                    e.currentTarget.querySelector(".zapocetIcon").style.display = "none";
                    e.currentTarget.querySelector(".zapocetSpin").style.display = "inline-flex";
                    htmx.ajax('POST', `/api/vysporiadanie/process-payment`, {
                        target: "#toast",
                        values: {
                            "data": JSON.stringify({
                                suma: formData.get("suma"),
                                forma: formData.get("forma"),
                                id: formData.get("id"),
                                ucetpartnera: formData.get("ucetpartnera"),
                                nazovpartnera: formData.get("nazovpartnera"),
                                datum: formData.get("datum"),
                                vs: formData.get("vs"),
                                ss: formData.get("ss"),
                                ks: formData.get("ks"),
                                cub: '<?php echo $cub; ?>',
                                mena: '<?php echo $mena; ?>'
                            })
                        }
                    }).then((response) => {
                    });
                    break;
            }
        });
    });


    $(".executeActionUhradaForm").on("submit", (e) => {
        e.preventDefault();
        const formData = new FormData(e.currentTarget);
        console.log(formData);
        const action = formData.get("action");
        htmx.ajax('POST', `/src/Controllers/global/vysporiadanie/penazne/addUhradaRow.php`, {
            target: "#toastCreate",
            values: {
                "data": JSON.stringify({
                    suma: formData.get("suma"),
                    id: formData.get("id"),
                    forma: formData.get("forma"),
                    ucetpartnera: formData.get("ucetpartnera"),
                    nazovpartnera: formData.get("nazpartnera"),
                    datum: formData.get("date"),
                    vs: formData.get("vs"),
                    ss: formData.get("ss"),
                    ks: formData.get("ks"),
                    cub: formData.get("cub"),
                    mena: formData.get("mena")
                }), "action": action
            }
        }).then((response) => {
        });
    });
</script>