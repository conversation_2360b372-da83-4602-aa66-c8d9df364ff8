<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/src/constants/global/globalCons.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/Payments/TransactionMatcher.class.php";
require_once('/home/<USER>/www/src/lib/Payments/Exceptions/PaymentProcessingException.php');

$cub = $_GET["ucet"];
$mena = $_GET["mena"];

if (isset($_SESSION["client"])) {
    $id_fond = $_SESSION["client"]["fondid"];
} else if ($_SESSION["mode"]["mode"] === "admin") {
    $id_fond = 1;
} else {
    $id_fond = 0;
}


$allowcheckneg = "0";
$allowcheckpoz = "0";

if ($id_fond !== 1) {
    if (in_array("1", $_SESSION["user"]["permissions"]) || in_array("4", $_SESSION["user"]["permissions"])) {
        $allowcheckneg = "1";
    }
    if (in_array("2", $_SESSION["user"]["permissions"])) {
        $allowcheckpoz = "1";
    }
}

if ($id_fond === 1) {
    if (in_array("1", $_SESSION["user"]["permissions"]) || in_array("2", $_SESSION["user"]["permissions"]) || in_array("4", $_SESSION["user"]["permissions"])) {
        $allowcheckneg = "1";
        $allowcheckpoz = "1";
    }
}

$kss = Connection::getDataFromDatabase("select * from konstantnysymbol", defaultDB)[1];
$query = "SELECT obratdatetime as obratdt,o.*
		from obratybu o
		where logactivityid in (4,5,6)
		and cub like '$cub'
		and subjektid=$id_fond
		and krdb=1
		order by suma, vs";
echo $query;
$ocakavaneUhrady = Connection::getDataFromDatabase($query, defaultDB)[1];
$pripraveneUhrady = [];
?>
<section class="py-4 relative px-5 sm:py-5" style="margin-bottom: 8rem">
    <div id="toast" class="z-30 top-26 fixed right-5"></div>
    <div class="mx-auto">
        <h2
            class="scroll-m-20 flex items-center gap-4 border-b mb-4 pb-2 text-3xl font-semibold dark:text-gray-100 tracking-tight first:mt-0">
            Spárovanie došlých platieb pre účet <span
                class="bg-green-100 text-green-800 text-md font-bold me-2 px-2 py-0.5 rounded-md dark:bg-gray-700 dark:text-green-400 border border-green-400"><?php echo $cub; ?></span>
        </h2>
    </div>
    <div id="ocakavaneUhradyWrapper" class="relative overflow-x-auto shadow-md sm:rounded-lg">
        <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400 overflow-hidden">
            <caption
                class="p-5 text-lg font-semibold text-left rtl:text-right text-gray-900 dark:text-gray-100 bg-white dark:text-white dark:bg-gray-800">
                <section class="flex items-center justify-between">
                    <div>
                        <span>Došlé platby</span>
                        <p class="mt-1 text-sm font-normal text-gray-500 dark:text-gray-400">List všetkých došlých
                            platieb.</p>
                    </div>
                </section>
            </caption>
            <thead
                class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:bg-gray-700 dark:text-gray-400">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        Suma
                    </th>
                    <th scope="col" class="px-6 py-3">
                        Účet partnera
                    </th>
                    <th scope="col" class="px-6 py-3">
                        Názov partnera
                    </th>
                    <th scope="col" class="px-6 py-3">
                        KS
                    </th>
                    <th scope="col" class="px-6 py-3">
                        Var. symbol
                    </th>
                    <th scope="col" class="px-6 py-3">
                        Špec. symbol
                    </th>
                    <th scope="col" class="px-6 py-3">
                        Dátum
                    </th>
                    <th scope="col" class="px-6 py-3">
                        Evidovať ako
                    </th>
                    <th scope="col" class="px-6 py-3">
                        Akcia
                    </th>
                </tr>
            </thead>
            <?php if (sizeof($ocakavaneUhrady) > 0) { ?>
                <tbody id="ocakavaneUhradyTBody" class="relative">
                    <?php foreach ($ocakavaneUhrady as $key => $uhrada) {
                        $vs = $uhrada["ks"];
                        $ss = $uhrada["ss"];
                        $logactivityid = $uhrada["logactivityid"];
                        $idplatba = $uhrada["id"];
                        $evidencia = [];
                        try {
                            $matcher = new TransactionMatcher((int) $idplatba, $logactivityid);

                            switch ($logactivityid) {
                                case '4':
                                    $evidencia = $matcher->indentifyPairing();
                                    break;
                                case '6':
                                    $evidencia = $matcher->indentifyPairing();
                                    print_r($evidencia);
                                    break;
                                case '9':
                                    $evidencia = [];
                                    // Unmatchable payment - no processing needed
                                    break;
                            }
                        } catch (\Exception $e) {
                            error_log('Transaction matching failed: ' . $e->getMessage());
                            //throw new PaymentProcessingException('Failed to match transaction: ' . $e->getMessage(), 0, $e);
                        }
                        ?>
                        <tr id="uhrada<?php echo $key; ?>"
                            class="bg-white border-b dark:bg-gray-800 transition-all ocakavanaUhrada cursor-pointer hover:bg-gray-200 hover:dark:bg-gray-600 dark:border-gray-700 border-gray-200">
                            <th scope="row"
                                class="px-6 py-2 font-medium text-gray-900 dark:text-gray-100 whitespace-nowrap dark:text-white">
                                <?php echo $uhrada["suma"]; ?>
                            </th>
                            <td class="px-6 py-2">
                                <?php echo $uhrada["cubpartnera"]; ?>
                            </td>
                            <td class="px-6 py-2">
                                <?php echo $uhrada["nazpartnera"]; ?>
                            </td>
                            <td class="px-6 py-2">
                                <?php echo $uhrada["ks"]; ?>
                            </td>
                            <td class="px-6 py-2">
                                <?php echo $uhrada["vs"]; ?>
                            </td>
                            <td class="px-6 py-2">
                                <?php echo $uhrada["ss"] == "" ? "-" : $uhrada["ss"]; ?>
                            </td>
                            <td class="px-6 py-2">
                                <?php echo $uhrada["obratdatetime"]; ?>
                            </td>
                            <td class="px-6 py-2">
                                <?php
                                if (is_array($evidencia)) {
                                    if (sizeof($evidencia) > 1) { ?>
                                        <select id="countries"
                                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                            <?php foreach ($evidencia as $key => $value) { ?>
                                                <option value="<?php echo $value["id"] ?>"><?php echo $value["name"] ?></option>
                                            <?php } ?>
                                        </select>
                                    <?php } else if (sizeof($evidencia) === 1) { ?>
                                            <input type="hidden" name="kodobratu" value="<?php echo $evidencia[0]["id"] ?>" />
                                            <span><?php echo $evidencia[0]["name"] ?></span>
                                    <?php } else { ?>
                                            <span class="text-red-500 font-bold">Nespárovateľná platba</span>
                                            <input type="hidden" name="kodobratu" value="219">
                                    <?php }
                                }
                                ?>
                            </td>
                            <td class="px-6 py-2">
                                <button type="submit" data-modal-target="sparovanieConfirm<?php echo $key; ?>"
                                    data-modal-toggle="sparovanieConfirm<?php echo $key; ?>" class="p-2 text-sm font-medium text-gray-900 bg-white rounded-lg hover:bg-gray-200 transition-all 
                                        hover:text-blue-700 focus:z-10 focus:ring-2 focus:ring-blue-700 focus:text-blue-700 dark:bg-gray-800 dark:text-white dark:hover:text-white 
                                        dark:hover:bg-gray-700 dark:focus:ring-blue-500 
                                        dark:focus:text-white">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-send">
                                        <path
                                            d="M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z" />
                                        <path d="m21.854 2.147-10.94 10.939" />
                                    </svg>
                                </button>
                            </td>
                        </tr>

                    <?php } ?>
                </tbody>
            <?php } else { ?>
                <tbody>
                    <tr id="waitingNoDataDiv"
                        class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200">
                        <td colspan="9"
                            class="px-6 py-4 text-center font-medium text-gray-900 dark:text-gray-100 whitespace-nowrap dark:text-white">
                            <div class="p-4 my-4 text-sm text-blue-800 rounded-lg bg-blue-50 w-full dark:bg-gray-700 dark:text-blue-400"
                                role="alert">
                                <span class="font-bold">Žiadne záznamy!</span> Podľa zadaných kritérii sme nenašli
                                žiadne
                                očakávané úhrady.
                            </div>
                        </td>
                    </tr>
                </tbody>
            <?php } ?>
        </table>
    </div>
</section>
<?php foreach ($ocakavaneUhrady as $key => $uhrada) {
    print_r($uhrada);
    $evidencia = [];
    $logactivityid = $uhrada["logactivityid"];
    $idplatba = $uhrada["id"];
    try {
        $matcher = new TransactionMatcher((int) $idplatba, $logactivityid);

        switch ($logactivityid) {
            case '4':
                $evidencia = $matcher->indentifyPairing();
                break;
            case '6':
                $evidencia = $matcher->indentifyPairing();
                break;
            case '9':
                $evidencia = [];
                // Unmatchable payment - no processing needed
                break;
        }
    } catch (\Exception $e) {
        error_log('Transaction matching failed: ' . $e->getMessage());
        //throw new PaymentProcessingException('Failed to match transaction: ' . $e->getMessage(), 0, $e);
    }

    ?>
    <div id="sparovanieConfirm<?php echo $key; ?>" tabindex="-1" aria-hidden="true" class="hidden overflow-y-auto bg-clip-padding backdrop-filter backdrop-blur-sm bg-opacity-10 dark:bg-gray-400/30 bg-white/60 overflow-x-hidden 
    fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 max-h-full flex">
        <div class="toastCreate absolute top-5 right-5 left-5"></div>
        <div class="relative p-4 w-full max-w-2xl max-h-full">
            <form class="sparujPlatbuForm">
                <input type="hidden" name="uhradaid" value="<?php echo $uhrada["id"] ?>">
                <input type="hidden" name="suma" value="<?php echo $uhrada["suma"] ?>">
                <input type="hidden" name="vs" value="<?php echo $uhrad["vs"]; ?>">
                <input type="hidden" name="ucetpartnera" value="<?php echo $uhrada["cubpartnera"] ?>">
                <input type="hidden" name="ss" value="<?php echo $uhrada["ss"]; ?>">
                <input type="hidden" name="ks" value="<?php echo $uhrada["ks"]; ?>">
                <input type="hidden" name="nazpartnera" value="<?php echo $uhrada["nazpartnera"] ?>">
                <input type="hidden" name="destinacia" value="<?php echo $evidencia[0]["destinacia"] ?>" />
                <input type="hidden" name="datum" value="<?php echo $uhrada["obratdatetime"] ?>">
                <input type="hidden" name="tranza" value="<?php echo $evidencia[0]["tranza"] ?>">
                <input type="hidden" name="logactivityid" value="<?php echo $uhrada["logactivityid"] ?>" />
                <input type="hidden" name="logdatatimeactivity" value="<?php echo date("Y-m-d"); ?>" />
                <input type="hidden" name="mena" value="<?php echo $mena; ?>">
                <input type="hidden" name="dealid" value="<?php echo $evidencia[0]["dealid"]; ?>">
                <input type="hidden" name="cub" value="<?php echo $cub; ?>">
                <input type="submit" class="hidden" id="moveUhradaFormOriginalSubmit">
                <div class="relative bg-white rounded-lg shadow-sm dark:bg-gray-700">
                    <div
                        class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600 border-gray-200">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                            Chcete spárovať túto platbu? [<?php echo $uhrada["id"]; ?>]
                        </h3>
                        <button type="button"
                            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                            data-modal-hide="sparovanieConfirm<?php echo $key; ?>">
                            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                                viewBox="0 0 14 14">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                            </svg>
                            <span class="sr-only">Close modal</span>
                        </button>
                    </div>
                    <div class="p-4 md:p-5 space-y-4 grid grid-cols-3 gap-3">
                        <div
                            class="max-w-sm p-3 h-full bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                            <a href="#">
                                <h5 class="mb-2 text-2xl font-bold tracking-tight text-gray-900 dark:text-white">
                                    <?php echo $uhrada["suma"]; ?>
                                </h5>
                            </a>
                            <p class="font-normal text-gray-700 dark:text-gray-400">Suma</p>
                        </div>
                        <div
                            class="max-w-sm p-3 h-full bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                            <a href="#">
                                <h5 class="mb-2 text-2xl font-bold tracking-tight text-gray-900 dark:text-white">
                                    <?php echo $uhrada["vs"]; ?>
                                </h5>
                            </a>
                            <p class="font-normal text-gray-700 dark:text-gray-400">Variabilný symbol</p>
                        </div>
                        <div
                            class="max-w-sm p-3 h-full bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                            <a href="#">
                                <h5 class="mb-2 text-2xl font-bold tracking-tight text-gray-900 dark:text-white">
                                    <?php echo $uhrada["ks"]; ?>
                                </h5>
                            </a>
                            <p class="font-normal text-gray-700 dark:text-gray-400">Konštantný symbol</p>
                        </div>
                        <div
                            class="max-w-sm p-3 h-full bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                            <a href="#">
                                <h5 class="mb-2 text-2xl font-bold tracking-tight text-gray-900 dark:text-white">
                                    <?php echo $uhrada["nazpartnera"]; ?>
                                </h5>
                            </a>
                            <p class="font-normal text-gray-700 dark:text-gray-400">Názov partnera</p>
                        </div>
                        <div
                            class="max-w-sm p-3 h-full bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                            <a href="#">
                                <h5 class="mb-2 text-2xl font-bold tracking-tight text-gray-900 dark:text-white">
                                    <?php echo $uhrada["cubpartnera"]; ?>
                                </h5>
                            </a>
                            <p class="font-normal text-gray-700 dark:text-gray-400">Účet partnera</p>
                        </div>
                        <div
                            class="max-w-sm p-3 h-full bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                            <a href="#">
                                <h5 class="mb-2 text-2xl font-bold tracking-tight text-gray-900 dark:text-white">
                                    <?php echo $uhrada["obratdatetime"]; ?>
                                </h5>
                            </a>
                            <p class="font-normal text-gray-700 dark:text-gray-400">Dátum</p>
                        </div>
                        <div
                            class="p-3 col-span-3 h-full bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                            <p class="font-normal mb-4 text-gray-700 dark:text-gray-400">Platba bude evidovaná ako</p>
                            <?php
                            if (sizeof($evidencia) > 1) { ?>
                                <select name="kodobratu"
                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                    <?php foreach ($evidencia as $key => $value) { ?>
                                        <option value="<?php echo KODY_OBRATU_EVIDENCIA[$evidencia[0]["id"]]["kod"] ?>">
                                            <?php echo $value["name"] ?>
                                        </option>
                                    <?php } ?>
                                </select>
                            <?php } else if (sizeof($evidencia) === 1) {
                                switch ($evidencia[0]["id"]) {
                                    case '1':
                                        $kodik = '201';
                                        break;
                                    case '2':
                                        $kodik = '-201';
                                        break;
                                    case '3':
                                        $kodik = '203';
                                        break;
                                    case '4':
                                        $kodik = '204';
                                        break;
                                    case '5':
                                        $kodik = '205';
                                        break;
                                    case '6':
                                        $kodik = '206';
                                        break;
                                    case '7':
                                        $kodik = '208';
                                        break;
                                    case '8':
                                        $kodik = '209';
                                        break;
                                    case '9':
                                        $kodik = '210';
                                        break;
                                    case '10':
                                        $kodik = '221';
                                        break;
                                    case '11':
                                        $kodik = '222';
                                        break;
                                    case '12':
                                        $kodik = '223';
                                        break;
                                    case '13':
                                        $kodik = '224';
                                        break;
                                    case '14':
                                        $kodik = '225';
                                        break;
                                    case '15':
                                        $kodik = '211';
                                        break;
                                    case '16':
                                        $kodik = '216';
                                        break;
                                    case '17':
                                        $kodik = '231';
                                        break;
                                    case '18':
                                        $kodik = '233';
                                        break;
                                    case '19':
                                        $kodik = '235';
                                        break;
                                    case '20':
                                        $kodik = '236';
                                        break;
                                    case '21':
                                        $kodik = '237';
                                        break;
                                    case '22':
                                        $kodik = '217';
                                        break;
                                    case '23':
                                        $kodik = '240';
                                        break;
                                    case '24':
                                        $kodik = '241';
                                        break;
                                    case '27':
                                        $kodik = '284';
                                        break;
                                }
                                ?>
                                    <h5 class="mb-2 text-2xl font-bold tracking-tight text-gray-900 dark:text-white">
                                    <?php echo KODY_OBRATU_EVIDENCIA[(int) $kodik]; ?>
                                    </h5>
                                    <input type="hidden" name="kodobratu" value="<?php echo $kodik; ?>">
                            <?php } else {
                                $nesparovatelna_options = array(
                                    '214' => 'Platba finančné výnosy',
                                    '226' => 'Vlastné výkony',
                                    '227' => 'Prevádzkové výnosy',
                                    '228' => 'Platba vkladu akcionára',
                                    '229' => 'Mimoriadny výnos'
                                );
                                ?>
                                    <select id="kodobratu" name="kodobratu"
                                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                    <?php foreach ($nesparovatelna_options as $key => $value) { ?>
                                            <option value="<?php echo $key ?>"><?php echo $value ?></option>
                                    <?php } ?>
                                    </select>
                            <?php }
                            ?>
                        </div>
                    </div>
                    <div class="flex items-center p-4 md:p-5 border-t border-gray-200 rounded-b dark:border-gray-600">
                        <button type="submit" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none 
                    focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center flex items-center gap-4
                        dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                            <span class="spanek">Áno, zevidovať
                                platbu</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                style="display: none"
                                class="lucide animate-spin zapocetSpin lucide-loader-circle-icon lucide-loader-circle">
                                <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                            </svg>
                        </button>
                        <button data-modal-hide="sparovanieConfirm<?php echo $key; ?>" type="button" class="py-2.5 px-5 ms-3 text-sm font-medium text-gray-900 focus:outline-none bg-white
                     rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 
                        focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 
                        dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">Zrušiť</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
<?php } ?>
<script>
    $(".sparujPlatbuForm").on("submit", (e) => {
        e.preventDefault();
        const formData = new FormData(e.currentTarget);
        console.log(formData);
        e.currentTarget.querySelector(".spanek").innerHTML = "Párujem platbu...";
        e.currentTarget.querySelector(".zapocetSpin").style.display = "inline-flex";
        htmx.ajax('POST', `/api/vysporiadanie/penazne/sparovanie/zauctovatPlatbu`, {
            target: ".toastCreate",
            values: formData
        }).then((response) => {
        });
    });
</script>