<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

if (isset($_SESSION["client"])) {
    $id_fond = $_SESSION["client"]["fondid"];
} elseif ($_SESSION["mode"]["mode"] === "admin") {
    $id_fond = 1;
} else {
    $id_fond = 0;
}

if ($id_fond === 0) {
    return null;
}

if (str_contains($_SERVER["REQUEST_URI"], "odchazajuce")) {
    $headingPage = "Úhrady odchádzajúcich peňazí";
    $urlPart = "/odchadzajuce";
    $inOut = 0;
} else {
    $headingPage = "Úhrady prichádzajúcich peňazí";
    $urlPart = "/prichadzajuce";
    $inOut = 1;
}

if (str_contains($_SERVER["REQUEST_URI"], "majetkove")) {
    $typ = "/majetkove";
} else if (str_contains($_SERVER["REQUEST_URI"], "penazne")) {
    $typ = "/penazne";
}

if (str_contains($_SERVER["REQUEST_URI"], "natipovanie")) {
    $action = "natipovanie";
    $countQuery = "SELECT count(dealid) as pocet, ucetaktiva
        from majetokcesta mc
        where subjektid = $id_fond
        and in_out = $inOut
        and tranza not in (select tranza
                            from uhrada
                            where uhrada.dealid = mc.dealid
                            and uhrada.subjektid = mc.subjektid
                            and logactivityid > 3)
        GROUP BY ucetaktiva";
} else if (str_contains($_SERVER["REQUEST_URI"], "sparovanie")) {
    $action = "sparovanie";
    $countQuery = "SELECT count(cub) as pocet, o.cub
		from obratybu o
		where logactivityid in (4,5,6)
		and subjektid = $id_fond
		and krdb=1
		group by cub";
}

$spravca = $id_fond == 1;
echo $countQuery;
if ($spravca) {
    $query = "SELECT * from spravcabu where spravcaid = $id_fond";
} else {
    $query = "SELECT * from fondsbu where fondid = $id_fond ORDER BY poradie";
}

$counts = Connection::getDataFromDatabase($countQuery, defaultDB)[1];

$ucty = Connection::getDataFromDatabase($query, defaultDB)[1];
$banky = Connection::getDataFromDatabase("select distinct banka, poradie from fondsbu where fondid = $id_fond and poradie is not null order by poradie asc", defaultDB)[1];
?>
<section class="py-4 px-5 sm:py-5" style="margin-bottom: 8rem">
    <div class="mx-auto">
        <section class="flex gap-4 items-center scroll-m-20 border-b mb-4 pb-2 ">
            <h2 class="text-3xl font-semibold dark:text-gray-100 tracking-tight first:mt-0">
                <?php echo $headingPage; ?>
            </h2>
            <div class="flex items-center gap-2">
                <form class="mb-0" id="filterCubsForm" hx-post="/api/vysporiadanie/filterCubs" hx-target="#gridik">
                    <input type="hidden" id="banka" name="banka" />
                    <button type="submit" id="filterCubsFormSubmitter" class="hidden"></button>
                </form>
                <?php foreach ($banky as $banka) {
                    switch ($banka["banka"]) {
                        case "ČSOB":
                            $color = "bg-blue-100 hover:bg-blue-300 hover:scale-105 text-blue-800 dark:bg-gray-700 dark:text-blue-400 border-blue-400";
                            break;
                        case "SLSP":
                            $color = "bg-red-100 hover:bg-red-300 hover:scale-105 text-red-800 dark:bg-gray-700 dark:text-red-400 border-red-400";
                            break;
                        case "EXANTE":
                            $color = "bg-green-100 hover:bg-green-300 hover:scale-105 text-green-800 dark:bg-gray-700 dark:text-green-400 border-green-400";
                            break;
                        case "J&T":
                            $color = "bg-yellow-100 hover:bg-yellow-300 hover:scale-105 text-yellow-800 dark:bg-gray-700 dark:text-yellow-400 border-yellow-400";
                            break;
                    }
                    ?>
                    <span
                        class="text-xs font-bold me-2 px-2.5 cubFilter transition-all cursor-pointer py-0.5 rounded-sm border <?php echo $color; ?>"><?php echo $banka["banka"]; ?></span>
                <?php } ?>
                <span
                    class="text-xs font-bold me-2 hover:bg-gray-200 px-2.5 cubFilterAll transition-all cursor-pointer py-0.5 rounded-sm border">Všetky</span>
            </div>
        </section>
        <div class="relative grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4" id="gridik">
            <?php foreach ($ucty as $key => $ucet) {
                switch ($ucet["banka"]) {
                    case "ČSOB":
                        $color = "bg-blue-100 hover:bg-blue-300 hover:scale-105 text-blue-800 dark:bg-gray-700 dark:text-blue-400 border-blue-400";
                        break;
                    case "SLSP":
                        $color = "bg-red-100 hover:bg-red-300 hover:scale-105 text-red-800 dark:bg-gray-700 dark:text-red-400 border-red-400";
                        break;
                    case "EXANTE":
                        $color = "bg-green-100 hover:bg-green-300 hover:scale-105 text-green-800 dark:bg-gray-700 dark:text-green-400 border-green-400";
                        break;
                    case "J&T":
                        $color = "bg-yellow-100 hover:bg-yellow-300 hover:scale-105 text-yellow-800 dark:bg-gray-700 dark:text-yellow-400 border-yellow-400";
                        break;
                }
                ?>
                <div hx-get="/vysporiadanie<?php echo $typ; ?><?php echo $urlPart; ?>/<?php echo $action; ?>/uhrada?ucet=<?php echo $ucet["cub"]; ?>&mena=<?php echo $ucet["mena"]; ?>"
                    hx-target="#pageContentMain" hx-replace-url="true"
                    class="relative block max-w-sm p-6 bg-white border cursor-pointer transition-all border-gray-200 rounded-lg shadow-sm hover:bg-gray-100 dark:bg-gray-800 dark:border-gray-700 dark:hover:bg-gray-700">
                    <h5 class="mb-2 text-2xl font-bold tracking-tight text-gray-900 dark:text-white">
                        <?php echo $ucet["cub"]; ?>
                    </h5>
                    <?php foreach ($counts as $count) {
                        if ($count["ucetaktiva"] === $ucet["cub"]) {
                            $pocet = $count["pocet"]; ?>
                            <span
                                class="text-md font-bold me-2 px-2.5 py-0.5 bg-red-400 text-white rounded-full absolute -right-4 -top-2 dark:bg-gray-700 dark:text-gray-300 border-gray-300"><?php echo $pocet; ?></span>
                        <?php }
                    } ?>
                    <div class="flex items-center w-full gap-1">
                        <span
                            class="bg-gray-300 text-gray-800 text-xs font-bold me-2 px-2.5 py-0.5 rounded-md shadow-md dark:bg-gray-900 dark:text-gray-300"><?php echo $ucet["mena"]; ?></span>
                        <span
                            class="text-xs font-bold me-2 px-2.5 transition-all cursor-pointer py-0.5 rounded-sm border <?php echo $color; ?>"><?php echo $ucet["banka"]; ?></span>
                    </div>
                </div>
            <?php } ?>
        </div>
    </div>
</section>
<script>
    $(".cubFilter").on("click", (e) => {
        const banka = e.currentTarget.innerText;
        document.getElementById("banka").value = banka;
        $(".cubFilter").removeClass("bg-blue-100");
        $("#filterCubsFormSubmitter").click();
    });

    $(".cubFilterAll").on("click", (e) => {
        document.getElementById("banka").value = "";
        $("#filterCubsFormSubmitter").click();
    });
</script>