<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>SAM</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class'
        }
    </script>
    <script>
        if (localStorage.getItem('color-theme') === 'dark' || (!('color-theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark')
        }
    </script>
    <link href="/src/assets/styles/index.css" rel="stylesheet" />
    <script src="/src/assets/js/index.global.js"></script>
    <script src="/src/assets/js/htmx.min.js"></script>
    <script src="/src/assets/js/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        let count = 0;
        htmx.onLoad(function (content) {
            initFlowbite();
        });
    </script>
</head>