<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/src/lib/functions/getDBSums.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/Components/klient/lineChart/chartObject.php";

$clientID = isset($matches[1]) ? intval($matches[1]) : null;
$client = Connection::getDataFromDatabase("SELECT p.*, po.fondid
FROM podielnik p
JOIN portfolio po ON p.podielnikid = po.podielnikid
WHERE p.podielnikid = $clientID", defaultDB)[1][0];

$pracovnikID =
    $fondID = $client["fondid"];
$portfoliaRes = Connection::getDataFromDatabase("SELECT po.* FROM podielnik p, portfolio po WHERE p.archiv = 'f' AND po.podielnikid = $clientID AND p.podielnikid = po.podielnikid ORDER BY po.fondid ASC", defaultDB);
$portfolia = $portfoliaRes[1];
$zastupujuci_vs = $client["zastupujuci_vs"];
$pracovnikRES = Connection::getDataFromDatabase("SELECT u.userid, u.username FROM users u INNER JOIN podielnik_sales ps ON id_sales = u.userid AND ps.id_podielnik = $clientID AND ps.datum_do = to_date('2099-12-31','YYYY-MM-DD')", defaultDB);
$pracovnik = $pracovnikRES[1][0];

$dbdate = date("Y-m-d", strtotime("- 1 day"));
$newdate = date("Y-m-d", strtotime("- 1 months - 1 day"));
$prostriedkySum = 0.00;
$dlhopisySum = 0.00;
$akcieSum = 0.00;
$podieloveSum = 0.00;
$zavazkySum = 0.00;
$totalSum = 0.00;
$podielProstriedky = 0.00;
$podielDlhopisy = 0.00;
$podielAkcie = 0.00;
$podielPodielove = 0.00;
$podielZavazky = 0.00;

foreach ($portfolia as $portfolio) {
    $fond = $portfolio["fondid"];
    $datumQuery = Connection::getDataFromDatabase("SELECT max(datum) FROM pricestore WHERE fondid = $fond", defaultDB);
    $dbdate = $datumQuery[1][0]["max"];
    $query = "SELECT menaref FROM majetoktotal WHERE datum=to_date('$dbdate','YYYY-MM-DD') ";
    if ($fond) {
        $query .= " ";
    } else if ($fond != "") {
        $query .= " and subjektid = $fond";
    }
    $queryRes = Connection::getDataFromDatabase($query, defaultDB);
    $result = $queryRes[1][0];

    $menaref = $result["menaref"];

    $p1 = DBSums::get($fond, $dbdate, "'BU','TD'", '221110,221210,315132,315113', false);
    $suma_p1 = round($p1, 2);

    $d1 = DBSums::get($fond, $dbdate, "'Bonds'", '251110,251120', false);
    $suma_d1 = round($d1, 2);

    $a1 = DBSums::get($fond, $dbdate, "'Shares'", '251200', false);
    $suma_a1 = round($a1, 2);

    $f1 = DBSums::get($fond, $dbdate, "'Fonds','Shares'", '251300,251200', false);
    $suma_f1 = round($f1, 2);

    $paz1 = DBSums::get($fond, $dbdate, "'Fonds','Bonds','Shares','TD','BU'", "", true);
    $suma_paz1 = round($paz1, 2);

    $total = (float) $p1 + (float) $d1 + (float) $a1 + (float) $f1 + (float) $paz1;
    $total_suma = round($total, 2);
    $totalSum += $total_suma;
    $prostriedkySum += $suma_p1;
    $dlhopisySum += $suma_d1;
    $akcieSum += $suma_a1;
    $podieloveSum += $suma_f1;
    $zavazkySum += $suma_paz1;

    if ($total == 0) {
        $total = 1;
    }

    $p2 = ($p1 / $total) * 100;
    $podiel_p2 = round($p2, 3);
    $podielProstriedky += $podiel_p2;
    $d2 = ($d1 / $total) * 100;
    $podiel_d2 = round($d2, 3);
    $podielDlhopisy += $podiel_d2;
    $a2 = ($a1 / $total) * 100;
    $podiel_a2 = round($a2, 3);
    $podielAkcie += $podiel_a2;
    $f2 = ($f1 / $total) * 100;
    $podiel_f2 = round($f2, 3);
    $podielPodielove += $podiel_f2;
    $paz2 = ($paz1 / $total) * 100;
    $podiel_paz2 = round($paz2, 3);
    $podielZavazky += $podiel_paz2;
}
?>
<section class="mt-40 lg:mt-5 pb-32 mb-40">
    <div class="px-9 mx-auto py-6">
        <div class="flex flex-col gap-6">
            <div
                class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 bg-white dark:bg-gray-700 dark:text-gray-200 p-6 rounded-lg border">
                <div class="flex items-center gap-4">
                    <div
                        class="h-16 w-16 rounded-full bg-primary-100 border-2 border-primary-500 overflow-hidden flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                            stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-circle-user-round-icon h-12 w-12 text-blue-300 lucide-circle-user-round">
                            <path d="M18 20a6 6 0 0 0-12 0" />
                            <circle cx="12" cy="10" r="4" />
                            <circle cx="12" cy="12" r="10" />
                        </svg>
                    </div>
                    <div>
                        <div class="flex items-center gap-2">
                            <h1 class="text-2xl font-bold"><?php echo $client["meno"] . " " . $client["prieznaz"] ?>
                            </h1>
                            <?php if ($client["archiv"] === "f") { ?>
                                <span
                                    class="bg-green-300 text-gray-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded-sm dark:bg-green-700 dark:text-gray-300">Aktívny</span>
                            <?php } else { ?>
                                <span
                                    class="bg-gray-300 text-gray-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded-sm dark:bg-gray-700 dark:text-gray-300">Archivovaný</span>
                            <?php } ?>
                            <?php if ($client["podielnikid_polaris"]) { ?>
                                <span
                                    class="bg-purple-300 text-gray-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded-sm dark:bg-purple-700 dark:text-gray-300">POLARIS</span>
                            <?php } ?>
                        </div>
                        <div class="flex items-center gap-4 mt-1">
                            <span class="text-sm text-gray-300">Registračné číslo:
                                <?php echo $client["podielnikid"] ?></span>
                            <span class="text-sm text-gray-400">Klientom od:
                                <?php echo $client["datetimeagreement"] ?></span>
                        </div>
                    </div>
                </div>
                <div class="flex flex-wrap gap-2 mt-4 md:mt-0">
                    <button
                        class="inline-flex items-center justify-center rounded-md text-sm font-medium px-3 py-2 bg-white
                        dark:hover:bg-gray-700 transition-all dark:bg-gray-500 dark:text-gray-200 text-gray-700 border border-gray-200 hover:bg-gray-50">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                        Poslať email
                    </button>
                    <button
                        class="inline-flex items-center justify-center rounded-md text-sm font-medium px-3 py-2 bg-white
                        dark:hover:bg-gray-700 transition-all dark:bg-gray-500 dark:text-gray-200 text-gray-700 border border-gray-200 hover:bg-gray-50">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>
                        Zavolať
                    </button>
                    <button
                        class="inline-flex items-center justify-center rounded-md text-sm font-medium px-3 py-2 bg-white
                        dark:hover:bg-gray-700 transition-all dark:bg-gray-500 dark:text-gray-200 text-gray-700 border border-gray-200 hover:bg-gray-50">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        Naplánovať stretnutie
                    </button>
                    <button class="inline-flex items-center justify-center rounded-md text-sm font-medium px-3 py-2 
                        transition-all text-white dark:bg-gray-900 dark:hover:bg-gray-800 hover:bg-primary-700">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                        Upraviť profil
                    </button>
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="md:col-span-2 bg-white dark:bg-gray-900 dark:text-gray-200 rounded-lg border shadow">
                    <div class="px-6 py-4 border-b">
                        <div class="flex justify-between items-center">
                            <div>
                                <h2 class="text-lg font-semibold">Osobné údaje</h2>
                                <p class="text-sm text-gray-500">Všetky informácie o klientovi</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm mb-1 text-gray-500">Zodpovedný pracovník</p>
                                <div hx-get="/pouzivatelia/detail/<?php echo $client["responsibleuserid"]; ?>"
                                    hx-target="#pageContentMain" hx-push-url="true" hx-replace-url="true"
                                    class="inline-flex items-center p-2 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 transition-all cursor-pointer justify-end gap-4">
                                    <div
                                        class="h-5 w-5 rounded-full bg-primary-100 border border-primary-500 overflow-hidden flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                            stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round"
                                            class="lucide lucide-circle-user-round-icon h-5 w-5 text-blue-300 lucide-circle-user-round">
                                            <path d="M18 20a6 6 0 0 0-12 0" />
                                            <circle cx="12" cy="10" r="4" />
                                            <circle cx="12" cy="12" r="10" />
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="flex justify-end items-center gap-2">
                                            <span class="text-sm font-bold">
                                                <?php echo $pracovnik["username"] ? $pracovnik["username"] : "Nebol priradený" ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                <h3 class="text-sm font-medium text-gray-500 mb-2">Základné informácie</h3>
                                <div class="space-y-2">
                                    <div
                                        class="flex justify-between hover:bg-gray-100 dark:hover:bg-gray-700 transition-all cursor-pointer p-0.5 px-1 rounded-lg">
                                        <span class="text-sm text-gray-300">Meno a priezvisko [Názov]</span>
                                        <span
                                            class="text-sm font-bold text-right"><?php echo $client["titulpred"] . " " . $client["meno"] . " " . $client["prieznaz"] . " " . $client["titulza"] ?></span>
                                    </div>
                                    <div
                                        class="flex justify-between hover:bg-gray-100 dark:hover:bg-gray-700 transition-all cursor-pointer p-0.5 px-1 rounded-lg">
                                        <span class="text-sm text-gray-300">Pohlavie</span>
                                        <span
                                            class="text-sm font-medium"><?php echo ($client["pohlavie"] === "z" ? "Žena" : ($client["pohlavie"] === "m" ? "Muž" : "Neuvedené")); ?></span>
                                    </div>
                                    <div
                                        class="flex justify-between hover:bg-gray-100 dark:hover:bg-gray-700 transition-all cursor-pointer p-0.5 px-1 rounded-lg">
                                        <span class="text-sm text-gray-300">Rodné číslo</span>
                                        <span class="text-sm font-medium"><?php echo $client["rcico"] ?></span>
                                    </div>
                                    <div
                                        class="flex justify-between hover:bg-gray-100 dark:hover:bg-gray-700 transition-all cursor-pointer p-0.5 px-1 rounded-lg">
                                        <span class="text-sm text-gray-300">Dátum narodenia</span>
                                        <span class="text-sm font-medium"><?php echo $client["datumnarodenia"] ?></span>
                                    </div>
                                    <div
                                        class="flex justify-between hover:bg-gray-100 dark:hover:bg-gray-700 transition-all cursor-pointer p-0.5 px-1 rounded-lg">
                                        <span class="text-sm text-gray-300">Číslo dokladu</span>
                                        <span class="text-sm font-medium"><?php echo $client["cisloid"] ?></span>
                                    </div>
                                    <div
                                        class="flex justify-between hover:bg-gray-100 dark:hover:bg-gray-700 transition-all cursor-pointer p-0.5 px-1 rounded-lg">
                                        <span class="text-sm text-gray-300">Typ klienta</span>
                                        <span class="text-sm font-medium"><?php echo $client["typklienta"] ?></span>
                                    </div>
                                    <div
                                        class="flex justify-between hover:bg-gray-100 dark:hover:bg-gray-700 transition-all cursor-pointer p-0.5 px-1 rounded-lg">
                                        <span class="text-sm text-gray-300">F/P osoba</span>
                                        <span
                                            class="text-sm font-medium"><?php echo $client["fpo"] === 1 ? "Fyzická osoba" : "Právnická osoba" ?></span>
                                    </div>
                                    <div
                                        class="flex justify-between hover:bg-gray-100 dark:hover:bg-gray-700 transition-all cursor-pointer p-0.5 px-1 rounded-lg">
                                        <span class="text-sm text-gray-300">Druh dokladu</span>
                                        <span
                                            class="text-sm font-medium"><?php echo $client["druhid"] === 0 ? "Občiansky preukaz" : ($client["druhid"] === 1 ? "Pas" : "Iné") ?></span>
                                    </div>
                                    <div
                                        class="flex justify-between hover:bg-gray-100 dark:hover:bg-gray-700 transition-all cursor-pointer p-0.5 px-1 rounded-lg">
                                        <span class="text-sm text-gray-300">Bežný účet</span>
                                        <span class="text-sm font-medium"><?php echo $client["bu"] ?></span>
                                    </div>
                                    <div
                                        class="flex justify-between hover:bg-gray-100 dark:hover:bg-gray-700 transition-all cursor-pointer p-0.5 px-1 rounded-lg">
                                        <span class="text-sm text-gray-300">Štát</span>
                                        <span class="text-sm font-medium"><?php echo Connection::getDataFromDatabase(
                                            "SELECT stateall FROM state WHERE stateid = " . $client["stateid"],
                                            defaultDB
                                        )[1][0]["stateall"]; ?>
                                        </span>
                                    </div>
                                    <div
                                        class="flex justify-between hover:bg-gray-100 dark:hover:bg-gray-700 transition-all cursor-pointer p-0.5 px-1 rounded-lg">
                                        <span class="text-sm text-gray-300">Sektor</span>
                                        <span class="text-sm font-medium"><?php echo Connection::getDataFromDatabase(
                                            "SELECT esa95_sektorpopis FROM sektor_esa95 WHERE esa95_sektorid = " . $client["sektor_esa95"],
                                            defaultDB
                                        )[1][0]["esa95_sektorpopis"]; ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex flex-col gap-4">
                                <section>
                                    <h3 class="text-sm font-medium text-gray-500 mb-2">Kontaktné informácie</h3>
                                    <div class="space-y-2">
                                        <div
                                            class="flex justify-between hover:bg-gray-100 dark:hover:bg-gray-700 transition-all cursor-pointer p-0.5 px-1 rounded-lg">
                                            <span class="text-sm text-gray-300">Email</span>
                                            <span
                                                class="text-sm font-medium"><?php echo $client["kontaktemail"] ?></span>
                                        </div>
                                        <div
                                            class="flex justify-between hover:bg-gray-100 dark:hover:bg-gray-700 transition-all cursor-pointer p-0.5 px-1 rounded-lg">
                                            <span class="text-sm text-gray-300">Telefón</span>
                                            <span
                                                class="text-sm font-medium"><?php echo $client["kontaktphonenumber"] ?></span>
                                        </div>
                                        <div
                                            class="flex justify-between hover:bg-gray-100 dark:hover:bg-gray-700 transition-all cursor-pointer p-0.5 px-1 rounded-lg">
                                            <span class="text-sm text-gray-300">Mobil</span>
                                            <span
                                                class="text-sm font-medium truncate max-w-[180px]"><?php echo $client["kontaktmobilnumber"] ?></span>
                                        </div>
                                        <div
                                            class="flex justify-between hover:bg-gray-100 dark:hover:bg-gray-700 transition-all cursor-pointer p-0.5 px-1 rounded-lg">
                                            <span class="text-sm text-gray-300">Ulica</span>
                                            <span
                                                class="text-sm font-medium"><?php echo $client["kontaktaddress"] ?></span>
                                        </div>
                                        <div
                                            class="flex justify-between hover:bg-gray-100 dark:hover:bg-gray-700 transition-all cursor-pointer p-0.5 px-1 rounded-lg">
                                            <span class="text-sm text-gray-300">Mesto</span>
                                            <span
                                                class="text-sm font-medium"><?php echo $client["kontaktcity"] ?></span>
                                        </div>
                                        <div
                                            class="flex justify-between hover:bg-gray-100 dark:hover:bg-gray-700 transition-all cursor-pointer p-0.5 px-1 rounded-lg">
                                            <span class="text-sm text-gray-300">PSČ</span>
                                            <span
                                                class="text-sm font-medium"><?php echo $client["kontaktpostalcode"] ?></span>
                                        </div>
                                    </div>
                                </section>
                                <section>
                                    <h3 class="text-sm font-medium text-gray-500 mb-2">Informácie o výpisoch</h3>
                                    <div class="space-y-2">
                                        <div
                                            class="flex justify-between hover:bg-gray-100 dark:hover:bg-gray-700 transition-all cursor-pointer p-0.5 px-1 rounded-lg">
                                            <span class="text-sm text-gray-300">Frekvencia výpisov</span>
                                            <span
                                                class="text-sm font-medium"><?php echo $client["vypisfrekvencia"] ?></span>
                                        </div>
                                        <div
                                            class="flex justify-between hover:bg-gray-100 dark:hover:bg-gray-700 transition-all cursor-pointer p-0.5 px-1 rounded-lg">
                                            <span class="text-sm text-gray-300">Je súčasťou GFI</span>
                                            <span
                                                class="text-sm font-medium"><?php echo $client["gfi"] === 1 ? "Áno" : "Nie" ?></span>
                                        </div>
                                    </div>
                                </section>
                                <section>
                                    <h3 class="text-sm font-medium text-gray-500 mb-2">Služby tretích strán</h3>
                                    <div class="space-y-2">
                                        <div
                                            class="flex justify-between hover:bg-gray-100 dark:hover:bg-gray-700 transition-all cursor-pointer p-0.5 px-1 rounded-lg">
                                            <span class="text-sm text-gray-300">Refundácia dane</span>
                                            <span
                                                class="text-sm font-medium"><?php echo $client["refundaciadane"] ? "Áno" : "Nie" ?></span>
                                        </div>
                                    </div>
                                </section>
                            </div>
                            <div class="flex flex-col gap-4">
                                <section>
                                    <h3 class="text-sm font-medium text-gray-500 mb-2">AML</h3>
                                    <div class="space-y-2">
                                        <div
                                            class="flex justify-between hover:bg-gray-100 dark:hover:bg-gray-700 transition-all cursor-pointer p-0.5 px-1 rounded-lg">
                                            <span class="text-sm text-gray-300">Politicky exponovaná osoba</span>
                                            <span
                                                class="text-sm font-medium"><?php echo $client["is_pep"] === 1 ? "Áno" : "Nie" ?></span>
                                        </div>
                                        <div
                                            class="flex justify-between hover:bg-gray-100 dark:hover:bg-gray-700 transition-all cursor-pointer p-0.5 px-1 rounded-lg">
                                            <span class="text-sm text-gray-300">Kategória AML rizika</span>
                                            <span
                                                class="text-sm font-medium"><?php echo $client["uroven_aml_rizika"] === 1 ? "Nízke" : ($client["uroven_aml_rizika"] === 2 ? "Stredné" : "Vysoké") ?></span>
                                        </div>
                                    </div>
                                </section>
                                <section>
                                    <h3 class="text-sm font-medium text-gray-500 mb-2">Daňové informácie</h3>
                                    <div class="space-y-2">
                                        <div
                                            class="flex justify-between hover:bg-gray-100 dark:hover:bg-gray-700 transition-all cursor-pointer p-0.5 px-1 rounded-lg">
                                            <span class="text-sm text-gray-300">Daňový domicil</span>
                                            <span class="text-sm font-medium"><?php echo $client["dan_domicil"] ? Connection::getDataFromDatabase(
                                                "SELECT stateall FROM state WHERE stateid = " . $client["dan_domicil"],
                                                defaultDB
                                            )[1][0]["stateall"] : "Neuvedené" ?>
                                            </span>
                                        </div>
                                        <div
                                            class="flex justify-between hover:bg-gray-100 dark:hover:bg-gray-700 transition-all cursor-pointer p-0.5 px-1 rounded-lg">
                                            <span class="text-sm text-gray-300">Typ zdanenia</span>
                                            <span
                                                class="text-sm font-medium"><?php echo $client["typ_zdanenia"] ?></span>
                                        </div>
                                    </div>
                                </section>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Financial Summary -->
                <div class="bg-white dark:bg-gray-900 dark:text-gray-200 rounded-lg border shadow">
                    <div class="px-6 py-4 border-b">
                        <h2 class="text-lg font-semibold">Finančný prehľad</h2>
                        <p class="text-sm text-gray-500">Finačný prehľad klienta s alokáciou aktív</p>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div>
                                <div class="flex justify-between mb-1">
                                    <span class="text-sm font-medium">Celková hodnota portfólií</span>
                                    <span
                                        class="text-lg text-blue-400 font-bold"><?php echo number_format($totalSum, 2, '.', ' '); ?>
                                        <?php echo $menaref ?></span>
                                </div>
                                <!-- <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: 85%"></div>
                                </div>
                                <div class="flex justify-between mt-1 text-xs text-gray-500">
                                    <span>Target: $1.5M</span>
                                    <span>85% of goal</span>
                                </div> -->
                            </div>
                            <hr />
                            <div class="pt-2">
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm font-bold">Alokácia aktív</span>
                                </div>
                                <div class="space-y-2">
                                    <div class="flex justify-between items-center">
                                        <div class="flex items-center gap-2">
                                            <div class="w-3 h-3 rounded-full" style="background-color: #36A2EB;"></div>
                                            <span class="text-sm">Peňažné prostriedky</span>
                                        </div>
                                        <div class="flex items-center gap-8">
                                            <span class="text-sm font-medium"><?php echo $podielProstriedky; ?>%</span>
                                            <span
                                                class="text-xs text-gray-500"><?php echo number_format($prostriedkySum, 2, '.', ' '); ?>
                                                &nbsp;<?php echo $menaref ?></span>
                                        </div>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <div class="flex items-center gap-2">
                                            <div class="w-3 h-3 rounded-full" style="background-color: #FF6384;"></div>
                                            <span class="text-sm">Dlhopisy</span>
                                        </div>
                                        <div class="flex items-center gap-8">
                                            <span class="text-sm font-medium"><?php echo $podielDlhopisy; ?>%</span>
                                            <span
                                                class="text-xs text-gray-500"><?php echo number_format($dlhopisySum, 2, '.', ' '); ?>&nbsp;<?php echo $menaref ?></span>
                                        </div>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <div class="flex items-center gap-2">
                                            <div class="w-3 h-3 rounded-full" style="background-color: #FF9F40;"></div>

                                            <span class="text-sm">Akcie</span>
                                        </div>

                                        <div class="flex items-center gap-8">
                                            <span class="text-sm font-medium"><?php echo $podielAkcie; ?>%</span>
                                            <span
                                                class="text-xs text-gray-500"><?php echo number_format($akcieSum, 2, '.', ' '); ?>
                                                <?php echo $menaref ?></span>
                                        </div>
                                    </div>

                                    <div class="flex justify-between items-center">
                                        <div class="flex items-center gap-2">
                                            <div class="w-3 h-3 rounded-full" style="background-color: #FFCD56;">
                                            </div>
                                            <span class="text-sm">Podielové listy a ETF</span>
                                        </div>
                                        <div class="flex items-center gap-8">
                                            <span class="text-sm font-medium"><?php echo $podielPodielove; ?>%</span>
                                            <span
                                                class="text-xs text-gray-500"><?php echo number_format($podieloveSum, 2, '.', ' '); ?>
                                                &nbsp;<?php echo $menaref ?></span>
                                        </div>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <div class="flex items-center gap-2 w-full">
                                            <div class="w-3 h-3 rounded-full" style="background-color: #4BC0C0;">
                                            </div>
                                            <span class="text-sm">Záväzky a pohľadávky</span>
                                        </div>
                                        <div class="flex items-center gap-8">
                                            <span class="text-sm font-medium"><?php echo $podielZavazky; ?>%</span>
                                            <span
                                                class="text-xs text-gray-500"><?php echo number_format($zavazkySum, 2, '.', ' '); ?>&nbsp;<?php echo $menaref ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div
                                style="position: relative; display: flex; justify-content: center; align-items: center; max-width: 100%; min-height: 7vw">
                                <canvas id="chartALL" class="absolute"></canvas>
                                <p class="text-xs font-bold"><?php echo number_format($totalSum, 2, '.', ' '); ?></p>
                            </div>
                            <script>
                                let prostriedky = <?php echo $prostriedkySum; ?>;
                                let dlhopisy = <?php echo $dlhopisySum; ?>;
                                let akcie = <?php echo $akcieSum; ?>;
                                let podielove = <?php echo $podieloveSum; ?>;
                                let zavazky = <?php echo $zavazkySum; ?>;
                                if (prostriedky === 0 && dlhopisy === 0 && akcie === 0 && podielove === 0 && zavazky === 0) {
                                    document.getElementById('chartik<?php echo $fond; ?>').parentNode.style.minHeight = "10vw";
                                    document.getElementById('chartik<?php echo $fond; ?>').parentNode.style.minWidth = "10vw";
                                    document.getElementById('chartik<?php echo $fond; ?>').parentNode.innerHTML = "<p class='font-bold bg-gray-300 p-1 px-2 rounded-lg'>Žiadne dáta...</p>";
                                }
                                let ctx = document.getElementById('chartALL');
                                new Chart(ctx, {
                                    type: 'doughnut',
                                    data: {
                                        labels: ['Peňažné prostriedky', 'Dlhopisy', 'Akcie, ADR a GDR', 'Podielové listy a ETF', 'Záväzky a pohľadávky'],
                                        datasets: [{
                                            label: '<?php echo $refmena; ?>',
                                            data: [prostriedky, dlhopisy, akcie, podielove, zavazky],
                                            borderWidth: 1
                                        }]
                                    },
                                    options: {
                                        responsive: true,
                                        maintainAspectRatio: true,
                                        plugins: {
                                            legend: {
                                                display: false
                                            }
                                        }
                                    }
                                });
                            </script>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="px-9">
        <ul
            class="text-sm font-medium text-center text-gray-500 rounded-lg shadow-sm sm:flex dark:bg-gray-900 p-2 gap-2 dark:text-gray-400">
            <li class="w-full focus-within:z-10">
                <button hx-get="/api/klienti/get/portfolio?id=<?php echo $clientID; ?>" hx-target="#tabContentResult"
                    class="inline-block w-full p-2 text-gray-900 bg-gray-100 sectionSelect rounded-lg transition-all 
                    dark:bg-gray-700 dark:text-white" aria-current="page">Portfólia</button>
            </li>
            <li class="w-full focus-within:z-10">
                <button hx-get="/api/klienti/get/transactions?id=<?php echo $clientID; ?>&page=1&limit=30"
                    hx-target="#tabContentResult" class="inline-block w-full p-2  hover:text-gray-700 hover:bg-gray-50 transition-all rounded-lg sectionSelect
                    dark:hover:text-white  dark:hover:bg-gray-700">Transakcie</button>
            </li>
            <li class="w-full focus-within:z-10">
                <button
                    hx-get="/api/klienti/get/documents?id=<?php echo $clientID; ?>&zastupujuci_vs=<?php echo $zastupujuci_vs; ?>&meno=<?php echo $client['meno'] . " " . $client['prieznaz'] ?>"
                    hx-target="#tabContentResult" class="inline-block w-full p-2  hover:text-gray-700 hover:bg-gray-50 transition-all rounded-lg sectionSelect
                    dark:hover:text-white  dark:hover:bg-gray-700">Dokumenty</button>
            </li>
            <li class="w-full focus-within:z-10">
                <button hx-get="/api/klienti/get/notes?id=<?php echo $clientID; ?>" hx-target="#tabContentResult" class="inline-block w-full p-2 hover:text-gray-700 hover:bg-gray-50 transition-all rounded-lg sectionSelect
                     dark:hover:text-white dark:hover:bg-gray-700">Poznámky</button>
            </li>
            <li class="w-full focus-within:z-10">
                <button hx-get="/api/klienti/get/acitivity?id=<?php echo $clientID; ?>" hx-target="#tabContentResult"
                    class="inline-block w-full p-2 hover:text-gray-700 hover:bg-gray-50 transition-all rounded-lg sectionSelect
                     dark:hover:text-white dark:hover:bg-gray-700">Aktivita</button>
            </li>
        </ul>
    </div>
    <div hx-trigger="load" hx-target="#tabContentResult" hx-swap="innerHTML"
        hx-post="/api/klienti/get/portfolio?id=<?php echo $clientID; ?>" class="p-9 dark:text-gray-200">
        <section id="tabContentResult">
            <span></span>
        </section>
        <div id="contentLoading" class="flex flex-col w-full h-96 gap-4 items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-loader-circle-icon animate-spin lucide-loader-circle">
                <path d="M21 12a9 9 0 1 1-6.219-8.56" />
            </svg>
            <span>Načítavam...</span>
        </div>
    </div>
    <script>
        $(".sectionSelect").on("click", (e) => {
            $(".sectionSelect").removeClass("dark:bg-gray-700");
            $(".sectionSelect").removeClass("dark:text-white");
            $(".sectionSelect").addClass("dark:text-gray-400");
            $(".sectionSelect").removeClass("bg-gray-100");
            e.target.classList.add("dark:bg-gray-700");
            e.target.classList.add("dark:text-white");
            e.target.classList.add("bg-gray-100");
        });
    </script>
</section>
<div id="toast" class="absolute right-5 top-1"></div>
<section class="mt-6">
</section>
<?php // LineChart::render($newdate, $dbdate, $portfolia, $clientID); ?>
<!-- <script src="/src/assets/js/klienti/dotaznik.js"></script> -->
</section>
<div id="add-modal" tabindex="-1" aria-hidden="true"
    class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center flex-wrap w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative p-4 w-full max-w-2xl max-h-full">
        <form id="dotaznickovyForm">
            <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                <div
                    class="flex items-center justify-between p-4 md:p-5 border-b flex-wrap rounded-t dark:border-gray-600">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                        Pridať investičný dotazník ku klientovi
                    </h3>
                    <button type="button"
                        class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                        data-modal-hide="add-modal">
                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                            viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                        </svg>
                        <span class="sr-only">Zavrieť</span>
                    </button>
                    <div class="p-4 md:p-5 space-y-4 w-full">
                        <input type="hidden" name="user_id" id="user_id"
                            value="<?php echo $client["zastupujuci_vs"]; ?>" />
                        <input type="hidden" name="redirect" id="redirect" value="false" />
                        <div class="flex mt-8 items-center justify-center w-full">
                            <label id="labelFileToUpload" for="fileToUpload"
                                class="flex flex-col items-center justify-center w-full h-64 border-2 flex-wrap border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:hover:bg-bray-800 dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500 dark:hover:bg-gray-600">
                                <div class="flex flex-col items-center flex-wrap justify-center pt-5 pb-6">
                                    <svg id="pdfIcon" class="w-8 hidden h-8 mb-4 text-gray-800 dark:text-white"
                                        aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        fill="currentColor" viewBox="0 0 24 24">
                                        <path fill-rule="evenodd"
                                            d="M9 2.221V7H4.221a2 2 0 0 1 .365-.5L8.5 2.586A2 2 0 0 1 9 2.22ZM11 2v5a2 2 0 0 1-2 2H4a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2 2 2 0 0 0 2 2h12a2 2 0 0 0 2-2 2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2V4a2 2 0 0 0-2-2h-7Zm-6 9a1 1 0 0 0-1 1v5a1 1 0 1 0 2 0v-1h.5a2.5 2.5 0 0 0 0-5H5Zm1.5 3H6v-1h.5a.5.5 0 0 1 0 1Zm4.5-3a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h1.376A2.626 2.626 0 0 0 15 15.375v-1.75A2.626 2.626 0 0 0 12.375 11H11Zm1 5v-3h.375a.626.626 0 0 1 .625.626v1.748a.625.625 0 0 1-.626.626H12Zm5-5a1 1 0 0 0-1 1v5a1 1 0 1 0 2 0v-1h1a1 1 0 1 0 0-2h-1v-1h1a1 1 0 1 0 0-2h-2Z"
                                            clip-rule="evenodd" />
                                    </svg>
                                    <svg id="uploadIcon" class="w-8 h-8 mb-4 text-gray-500 dark:text-gray-400"
                                        aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 20 16">
                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2" />
                                    </svg>
                                    <p id="futureFile" class="mb-2 text-sm text-gray-500 dark:text-gray-400"><span
                                            class="font-semibold">Klikni pre nahratie súboru</span></p>
                                    <p id="deleteText" class="text-xs text-gray-500 dark:text-gray-400">PNG, JPG alebo
                                        PDF (MAX. 1MB)</p>
                                </div>
                                <input id="fileToUpload" type="file" name="fileToUpload" class="hidden" />
                            </label>
                        </div>
                        <label for="first_name"
                            class="block mb-2 text-sm mt-6 font-medium text-gray-900 dark:text-white">Dátum
                            vyhotovenia:</label>
                        <input type="date" id="date" i name="date"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                            required />
                    </div>
                    <div
                        class="flex w-full flex-wrap items-center p-4 md:p-5 border-t border-gray-200 rounded-b dark:border-gray-600">
                        <p id="pdfUploadError" class="mb-3 font-bold text-red-500 w-full"></p>
                        <button type="submit"
                            class="text-white bg-blue-700 hover:bg-blue-800  font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                            Pridať
                        </button>
                        <button data-modal-hide="add-modal" type="button"
                            class="py-2.5 px-5 ms-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">
                            Zavrieť
                        </button>
        </form>
    </div>
</div>
<script>
    $(".sectionSelect").on("click", (e) => {
        console.log("I CLICKEDDDDDDDDD")
        $("#tabContentResult").css("display", "none");
        $("#contentLoading").css("display", "block");
    });

    document.addEventListener('htmx:afterRequest', function (evt) {
        $("#tabContentResult").css("display", "block");
        $("#contentLoading").css("display", "none");
    });

    document.addEventListener('htmx:afterSettle', function (evt) {
        $("#tabContentResult").css("display", "block");
        $("#contentLoading").css("display", "none");
    });
</script>
<script src="/src/assets/js/index.js"></script>
<script src="/src/assets/js/klienti/detail/transactions-table.js"></script>*/