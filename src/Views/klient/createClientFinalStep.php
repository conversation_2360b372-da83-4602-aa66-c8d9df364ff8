<?php
require "/home/<USER>/www/src/lib/connection.php";
require "/home/<USER>/www/conf/settings.php";

$clientID = isset($matches[1]) ? intval($matches[1]) : null;

if ($clientID === null) {
  header("Location: /");
}

$fondyRes = Connection::getDataFromDatabase("SELECT * FROM fonds WHERE template = 1 AND archiv = 'f'", defaultDB);
$fondy = $fondyRes[1];

$menaRes = Connection::getDataFromDatabase("SELECT * FROM menadb ORDER BY poradie ASC", defaultDB);
$meny = $menaRes[1];

$typeRes = Connection::getDataFromDatabase("SELECT * FROM typklienta", defaultDB);
$types = $typeRes[1];

?>
<div class="flex bg-white dark:bg-gray-900 text-gray-900 dark:text-white px-4 justify-between shadow-sm items-center">
  <div class="nevime flex items-center gap-2">
    <div class="flex mb-1">
      <a href="/klienti" class="hover:bg-gray-500 hover:text-white transition-all rounded-lg mt-1">
        <svg class="w-6 h-6" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none"
          viewBox="0 0 24 24">
          <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M5 12h14M5 12l4-4m-4 4 4 4" />
        </svg>
      </a>
    </div>
    <ol
      class="flex items-center w-full p-3 space-x-2 text-sm font-medium text-center text-gray-500 dark:text-gray-400 sm:text-base sm:p-4 sm:space-x-4 rtl:space-x-revers">
      <li class="flex items-center">
        <span
          class="flex items-center justify-center w-5 h-5 me-2 text-xs border-2 flex items-center justify-center font-bold border-green-500 text-green-500 rounded-full shrink-0">
          1
        </span><span class="text-green-500 font-bold">Osobné údaje</span>
        <svg class="w-3 h-3 ms-2 sm:ms-4 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
          fill="none" viewBox="0 0 12 10">
          <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="m7 9 4-4-4-4M1 9l4-4-4-4" />
        </svg>
      </li>
      <li class="flex items-center font-bold text-green-500">
        <span
          class="flex items-center justify-center w-5 h-5 me-2 text-xs border-2 border-green-500 font-bold rounded-full shrink-0 dark:border-gray-400">
          2
        </span>Investičný dotazník</span>
        <svg class="w-3 h-3 ms-2 sm:ms-4 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
          fill="none" viewBox="0 0 12 10">
          <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="m7 9 4-4-4-4M1 9l4-4-4-4" />
        </svg>
      </li>
      <li class="flex items-center text-blue-600">
        <span
          class="flex items-center justify-center w-5 h-5 me-2 text-xs border border-blue-500 rounded-full shrink-0">
          3
        </span>
        Zriadenie portfólia
      </li>
    </ol>
  </div>
  <?php if ($clientID !== null) {
    echo '<div class="flex items-center gap-4">
    <strong>ID zákazníka: </strong>
    <h2>' . $clientID . '</h2>
  </div>';
  } ?>
</div>
<section class="p-5">
  <div class="mt-4">
    <h2 class="text-2xl dark:text-white text-gray-900 font-bold">Výber portfólia</h2>
    <form id="vyberPortfolio">
      <div class="mt-4 mb-4">
        <label for="portfolioDate" class="block mb-2 text-sm w-full font-medium text-gray-900 dark:text-white">Dátum
          založenia portfólia:</label>
        <input type="date" id="portfolioDate" name="portfolioDate"
          class="bg-gray-50 border border-gray-300 text-gray-900 dark:bg-gray-700 dark:text-gray-200 text-sm rounded-lg w-1/2 focus:ring-blue-500 focus:border-blue-500 block p-2.5"
          required />
      </div>
      <div class="mt-4 mb-4">
        <label for="template" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Portfólio:</label>
        <select id="template" name="template"
          class="bg-gray-50 border border-gray-300 text-gray-900 dark:bg-gray-700 dark:text-gray-100 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-1/2 p-2.5">
          <?php foreach ($fondy as $key => $value) { ?>
            <option value="<?php echo $value['fondid']; ?>"><?php echo $value["fondnameall"] ?></option>
          <?php } ?>
        </select>
      </div>
      <button type="submit"
        class="focus:outline-none text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-green-600 dark:hover:bg-green-700 dark:focus:ring-green-800">Načítať
        šablónu portfólia</button>
    </form>
  </div>
</section>
<hr />
<section class="p-5">
  <h2 class="text-3xl dark:text-white text-gray-900 font-bold">Základné údaje</h2>
  <div class="mt-4 pt-2 pb-6 rounded-md shadow-md">
    <section class="flex gap-8 items-center">
      <div class="mt-4 mb-4 w-full">
        <label for="idNumber" class="block mb-2 text-sm w-full font-medium text-gray-900 dark:text-white">Identifikačné
          číslo:</label>
        <input type="text" disabled id="idNumber" name="idNumber"
          class="bg-gray-50 border border-gray-300 dark:bg-gray-700 dark:text-gray-100 p-2 text-gray-900 text-sm rounded-lg w-full focus:ring-blue-500 focus:border-blue-500 block"
          placeholder="Vygeneruje sa automaticky" />
      </div>
      <div class="mt-4 mb-4 w-full">
        <label for="status" class="block mb-2 text-sm w-full font-medium text-gray-900 dark:text-white">Status:</label>
        <input type="text" id="status" name="status"
          class="bg-gray-50 border inputFond dark:bg-gray-700 dark:text-gray-100 p-2 border-gray-300 text-gray-900 text-sm rounded-lg w-full focus:ring-blue-500 focus:border-blue-500 block"
          placeholder="" required />
      </div>
    </section>
    <section class="flex gap-8">
      <section class="w-full">
        <div class="mt-4 mb-4 w-full">
          <label for="nazov" class="block mb-2 text-sm w-full font-medium text-gray-900 dark:text-white">Názov:</label>
          <input type="text" id="nazov" name="nazov"
            class="bg-gray-50 inputFond border border-gray-300 dark:bg-gray-700 dark:text-gray-100 p-2 text-gray-900 text-sm rounded-lg w-full focus:ring-blue-500 focus:border-blue-500 block"
            placeholder="" required />
        </div>
        <div class="mt-4 mb-4 w-full">
          <label for="skratka"
            class="block mb-2 text-sm w-full font-medium text-gray-900 dark:text-white">Skratka:</label>
          <input type="text" id="skratka" name="skratka"
            class="bg-gray-50 border border-gray-300 dark:bg-gray-700 dark:text-gray-100 p-2 text-gray-900 inputFond text-sm rounded-lg w-full focus:ring-blue-500 focus:border-blue-500 block"
            placeholder="" required />
        </div>
        <div class="mt-4 mb-4 w-full">
          <label for="dateOfStart" class="block mb-2 text-sm w-full font-medium text-gray-900 dark:text-white">Dátum
            založenia:</label>
          <input type="date" id="dateOfStart" name="dateOfStart"
            class="bg-gray-50 border inputFond border-gray-300 dark:bg-gray-700 dark:text-gray-100 p-2 text-gray-900 text-sm rounded-lg w-full focus:ring-blue-500 focus:border-blue-500 block"
            placeholder="" required />
        </div>
        <div class="mt-4 mb-4">
          <label for="mena" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Referenčná
            mena:</label>
          <select id="mena"
            class="bg-gray-50 border border-gray-300 dark:bg-gray-700 dark:text-gray-100 p-2 inputFond text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full">
            <?php foreach ($meny as $key => $value) { ?>
              <option value="<?php echo $value['mena']; ?>"><?php echo $value["mena"] ?></option>
            <?php } ?>
          </select>
        </div>
      </section>
      <section class="w-full">
        <div class="mt-4 mb-4">
          <label for="typPredlohy" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Typ
            predlohy:</label>
          <select id="typPredlohy"
            class="bg-gray-50 border inputFond border-gray-300 dark:bg-gray-700 dark:text-gray-100 p-2 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full">
            <?php foreach ($types as $key => $value) { ?>
              <option value="<?php echo $value['typklienta']; ?>"><?php echo $value["popis"] ?></option>
            <?php } ?>
          </select>
        </div>
        <div class="mt-4 mb-4">
          <label for="uhradaPoplatkov" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Spôsob
            úhrady poplatkov:</label>
          <select id="uhradaPoplatkov"
            class="bg-gray-50 inputFond border border-gray-300 dark:bg-gray-700 dark:text-gray-100 p-2 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full">
            <option value="0">Faktúra</option>
            <option value="1">Inkaso</option>
          </select>
        </div>
        <div class="mt-4 mb-4 w-full">
          <label for="cielovaSuma" class="block mb-2 text-sm w-full font-medium text-gray-900 dark:text-white">Cieľová
            suma:</label>
          <input type="text" id="cielovaSuma" name="cielovaSuma"
            class="bg-gray-50 border border-gray-300 dark:bg-gray-700 dark:text-gray-100 p-2 text-gray-900 text-sm rounded-lg w-full focus:ring-blue-500 focus:border-blue-500 block"
            placeholder="" required />
        </div>
        <div class="">
          <label for="message" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Poznámka</label>
          <textarea id="message" rows="4"
            class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 dark:bg-gray-700 dark:text-gray-100 p-2 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
            placeholder=""></textarea>
        </div>
      </section>
    </section>
  </div>
</section>
<section class="p-5 dark:text-white text-gray-900">
  <h2 class="text-3xl font-bold">Účty</h2>
  <div class="mt-4 pt-2 pb-6 px-8 rounded-md shadow-md">
    <div class="flex mt-4 items-center justify-between">
      <h3 class="text-xl font-bold">Peňažné účty</h3>
    </div>
    <div class="relative mt-4 overflow-x-auto">
      <table id="penazneUctyTable"
        class="w-full hidden text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
          <tr>
            <th scope="col" class="px-6 py-3">
              Číslo účtu
            </th>
            <th scope="col" class="px-6 py-3">
              Mena
            </th>
            <th scope="col" class="px-6 py-3">
              Banka
            </th>
            <th scope="col" class="px-6 py-3">
            </th>
          </tr>
        </thead>
        <tbody id="penazneUcty"></tbody>
      </table>
      <p id="penazneUctyNotice" class="text-gray-400 text-center py-8">Žiadne údaje</p>
    </div>
    <div class="flex mt-4 items-center justify-between">
      <h3 class="text-xl font-bold">Majetkové účty</h3>
      <button type="button"
        class="focus:outline-none text-white bg-purple-700 hover:bg-purple-800 focus:ring-4 focus:ring-purple-300 font-medium rounded-lg text-sm px-5 py-1">Pridať</button>
    </div>
    <div class="relative mt-4 overflow-x-auto">
      <table id="majetkoveUctyTable"
        class="w-full hidden hidden text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
          <tr>
            <th scope="col" class="px-6 py-3">
              Číslo účtu
            </th>
            <th scope="col" class="px-6 py-3">
              Custodian
            </th>
            <th scope="col" class="px-6 py-3">
              Typ účtu
            </th>
            <th scope="col" class="px-6 py-3">
              Akcia
            </th>
          </tr>
        </thead>
        <tbody id="majetkoveUcty"></tbody>
      </table>
      <p id="majetkoveUctyNotice" class="text-gray-400 text-center py-8">Žiadne údaje</p>
    </div>
  </div>
</section>
<div id="createPenaznyUcetModal" tabindex="-1" aria-hidden="true"
  class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
  <div class="relative p-4 w-full max-w-2xl max-h-full">
    <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
      <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
        <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
          Pridať nový účet k peňažným účtom
        </h3>
        <button type="button"
          class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
          data-modal-hide="createPenaznyUcetModal">
          <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
          </svg>
          <span class="sr-only">Close modal</span>
        </button>
      </div>
      <div class="p-4 md:p-5 space-y-4">
      </div>
      <div class="flex items-center p-4 md:p-5 border-t border-gray-200 rounded-b dark:border-gray-600">
        <button type="button"
          class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">Vytvoriť</button>
        <button data-modal-hide="createPenaznyUcetModal" type="button"
          class="py-2.5 px-5 ms-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">Zavrieť</button>
      </div>
    </div>
  </div>
</div>
<script src="/src/assets/js/klienti/edit.js"></script>