<?php
require "/home/<USER>/www/src/lib/connection.php";
require "/home/<USER>/www/conf/settings.php";

$onboardingRes = Connection::getDataFromDatabase("SELECT * FROM users WHERE status='approved' AND updated_at>date_sub(curdate(), INTERVAL 31 DAY)
 AND onboarding_id IS NOT NULL and id>100 order by id desc", onboardingDB, "3312");
$onboarding = $onboardingRes[1];
$vs = Connection::getDataFromDatabase("SELECT zastupujuci_vs FROM podielnik WHERE zastupujuci_vs IS NOT NULL", defaultDB)[1];
foreach ($vs as $key => $value) {
    $vsIDs[] = $value["zastupujuci_vs"];
}
$ids = implode(",", $vsIDs);
$onboardingIDs = Connection::getDataFromDatabase("SELECT id, onboarding_id FROM users WHERE onboarding_id IS NOT NULL AND status = 'approved' AND id NOT IN ($ids)", onboardingDB, "3312")[1];

foreach ($onboarding as $key => $value) {
    $onboardingIDs1[] = $value["onboarding_id"];
}

foreach ($onboardingIDs as $key => $value) {
    $onboardingIDs2[] = $value["onboarding_id"];
}  
foreach ($onboarding as $key => $value) {
    if (in_array($value["onboarding_id"], $onboardingIDs2)) {
        $onboradingUsers[] = $value;
    }
}

print_r($onboradingUsers);

$statesRes = Connection::getDataFromDatabase("SELECT * FROM state", defaultDB);
$states = $statesRes[1];

$menaRes = Connection::getDataFromDatabase("SELECT * FROM menadb", defaultDB);
$meny = $menaRes[1];

$pobockaRes = Connection::getDataFromDatabase("SELECT * FROM pobocka", defaultDB);
$pobocka = $pobockaRes[1];

$clientTypesRes = Connection::getDataFromDatabase("SELECT * FROM typklienta", defaultDB);
$clientTypes = $clientTypesRes[1];

$usersRes = Connection::getDataFromDatabase("SELECT * FROM users WHERE sales = 1", defaultDB);
$users = $usersRes[1];

$sektorRes = Connection::getDataFromDatabase("SELECT * FROM sektor_esa95 order by esa95_sektoroznacenie", defaultDB);
$sectors = $sektorRes[1];
?>
<section class="relative">
    <div id="pulseLoading" class="animate-pulse bg-gray-400/50 h-full absolute z-20 w-full" style="display: none;">
    </div>
    <form id="clientCreate" class="relative w-full p-5 overflow-hidden">
        <input type="hidden" class="hidden" name="podielnikid">
        <div class="flex py-3 w-full justify-between  border-b border-black items-center">
            <h3 class="text-xl font-semibold text-gray-800 dark:text-white">Osobné údaje</h3>
            <div class="flex gap-4 items-center">
                <label for="countries" class="blockt text-sm font-medium text-gray-900 dark:text-white">Elektronický
                    onboarding: </label>
                <select id="getOnboardingData"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <option selected>Vyberte klienta</option>
                    <?php foreach ($onboradingUsers as $key => $value) { ?>
                        <option value='<?php echo json_encode($value) ?>'>
                            <?php echo $value["name_prefix"] . " " . $value["first_name"] . " " . $value["last_name"] ?>
                            [<strong><?php echo $value["created_at"] ?></strong>]
                        </option>
                    <?php } ?>
                </select>
            </div>
        </div>
        <div class="flex mt-3 gap-8">
            <div class="w-full bg-white rounded-xl p-2 px-4 flex flex-col gap-1 shadow-md">
                <div class="flex items-center mb-1">
                    <label for="fpo" class="block mb-2 w-full text-sm font-semibold text-gray-900 dark:text-white">F/P
                        osoba: </label>
                    <select id="fpo" name="fpo" required
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                        <option value="0">Fyzická osoba</option>
                        <option value="1">Právnická osoba</option>
                        <option value="2">Fyzická osoba - podnikateľ</option>
                    </select>
                </div>

                <div id="prieznazSection" class="flex items-center mb-1">
                    <label for="prieznaz"
                        class="block mb-2 w-full text-sm font-semibold text-gray-900 dark:text-white">Priezvisko/Názov:
                    </label>
                    <input type="text" id="prieznaz" name="prieznaz"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                        placeholder="Priezvisko" required />
                </div>
                <div id="menoSection" class="flex items-center mb-1">
                    <label for="meno"
                        class="block mb-2 w-full text-sm font-semibold text-gray-900 dark:text-white">Meno:
                    </label>
                    <input type="text" id="meno" name="meno"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                        placeholder="Meno" />
                </div>
                <div id="titulSection" class="flex items-center">
                    <p class="flex text-sm w-full justify-between">
                        <span class="font-semibold">Tituly: </span>
                    </p>
                    <section class="flex mr-1 gap-2 w-full justify-between">
                        <div class="flex items-center mb-1">
                            <input type="text" id="titulpred" name="titulpred"
                                class="bg-gray-50 w-full border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-0.5 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                placeholder="Titul pred menom" />
                        </div>
                        <div class="flex items-center mb-1">
                            <input type="text" id="titulza" name="titulza"
                                class="bg-gray-50 w-full border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-0.5 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                placeholder="Titul za menom" />
                        </div>
                    </section>
                </div>
                <div class="flex gap-3 items-center mb-1">
                    <label for="sex" class="block text-sm w-full font-semibold text-gray-900 dark:text-white">Typ
                        klienta:
                    </label>
                    <select id="typklienta" name="typklienta"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                        <?php
                        foreach ($clientTypes as $clientType) { ?>
                            <option value="<?php echo $clientType['typklienta']; ?>"><?php echo $clientType["popis"] ?>
                            </option>
                        <?php } ?>
                    </select>
                </div>
                <div class="flex items-center mb-1">
                    <label for="druhid"
                        class="block mb-2 w-full text-sm font-semibold text-gray-900 dark:text-white">Druh
                        dokladu: </label>
                    <select id="druhid" name="druhid"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                        <option value="0">Občiansky preukaz</option>
                        <option value="1">Pas</option>
                        <option value="2">Iné</option>
                    </select>
                </div>
            </div>
            <div class="w-full bg-white rounded-xl p-2 px-4 flex flex-col gap-1 shadow-md">
                <div id="foInputs" style="display: none;">
                    <div class="flex gap-3 items-center mb-1">
                        <label for="ico"
                            class="block mb-2 text-sm w-full font-semibold text-gray-900 dark:text-white">IČO:</label>
                        <input type="text" id="ico" name="ico"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                            placeholder="IČO" />
                    </div>
                    <div class="flex gap-3 items-center mb-1">
                        <label for="lei_kod" class="block mb-2 text-sm w-full font-semibold text-gray-900">LEI:</label>
                        <input type="text" id="lei_kod" name="lei_kod"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                            placeholder="LEI" />
                    </div>
                    <div class="flex gap-3 items-center mb-1">
                        <label for="dic" class="block mb-2 text-sm w-full font-semibold text-gray-900">DIC:</label>
                        <input type="text" id="dic" name="dic"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                            placeholder="DIČ" />
                    </div>
                    <div class="flex gap-3 items-center mb-1">
                        <label for="icdph" class="block mb-2 text-sm w-full font-semibold text-gray-900">DIČ
                            DPH:</label>
                        <input type="text" id="icdph" name="icdph"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                            placeholder="DIČ DPH" />
                    </div>
                </div>
                <div id="pohlavieWrapper" class="flex gap-3 items-center mb-1">
                    <label for="pohlavie"
                        class="block mb-2 text-sm w-full font-semibold text-gray-900 dark:text-white">Pohlavie</label>
                    <select id="pohlavie" name="pohlavie"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                        <option value="m">Muž</option>
                        <option value="z">Žena</option>
                        <option value="i">Iné</option>
                    </select>
                </div>
                <div id="rcicoWrapper" class="flex items-center mb-1">
                    <label for="rcico"
                        class="block mb-2 w-full text-sm font-semibold text-gray-900 dark:text-white">Rodné
                        číslo: </label>
                    <input type="text" id="rcico" name="rcico"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                        placeholder="Rodné číslo" required />
                </div>
                <div id="datum_narodeniaWrapper" class="flex items-center mb-1">
                    <label for="datum_narodenia"
                        class="block mb-2 w-full text-sm font-semibold text-gray-900 dark:text-white">Dátum
                        narodenia: </label>
                    <input type="date" id="datum_narodenia" name="datum_narodenia"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                        placeholder="Dátum narodenia" />
                </div>
                <div id="cisloidWrapper" class="flex items-center mb-1">
                    <label for="cisloid"
                        class="block mb-2 w-full text-sm font-semibold text-gray-900 dark:text-white">Číslo
                        dokladu: </label>
                    <input type="text" id="cisloid" name="cisloid"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                        placeholder="Číslo dokladu" />
                </div>
                <div id="stateidWrapper" class="flex items-center mb-1 text-gray-900 dark:text-white">
                    <label for="stateid" class="block mb-2 w-full text-sm font-semibold">Štát:
                    </label>
                    <select id="stateid" name="stateid"
                        class="bg-gray-50 border border-gray-300 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:focus:ring-blue-500 dark:focus:border-blue-500">
                        <?php
                        foreach ($states as $state) { ?>
                            <option value="<?php echo $state['stateid']; ?>"><?php echo $state["stateall"] ?></option>
                        <?php } ?>
                    </select>
                </div>
                <div class="flex items-center mb-1">
                    <label for="sektor_esa95"
                        class="block mb-2 w-full text-sm font-semibold text-gray-900 dark:text-white">Sektor: </label>
                    <select id="sektor_esa95" name="sektor_esa95"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                        <?php
                        foreach ($sectors as $sector) { ?>
                            <option value="<?php echo $sector['esa95_sektorid']; ?>">
                                <?php echo $sector["esa95_sektorpopis"] ?>
                            </option>
                        <?php } ?>
                    </select>
                </div>
            </div>
        </div>
        <h3 class="text-lg mt-3 mb-2 font-semibold dark:text-white">Bankové účty</h3>
        <div class="w-full bg-white rounded-xl py-6 px-4 shadow-md">
            <div id="accountsErrorMsg"
                class="hidden transition-all mb-4 flex justify-center bg-red-500 p-1 rounded-md text-white font-semibold">
            </div>
            <input type="hidden" class="hidden" name="accountsData" id="accountsData" />
            <div class="w-full relative flex justify-center items-center sm:rounded-lg">
                <table id="accountsTable"
                    class="w-full hidden text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <th scope="col" class="px-6 text-center py-3">
                                Číslo účtu
                            </th>
                            <th scope="col" class="px-6 text-center py-3">
                                Mena účtu
                            </th>
                            <th scope="col" class="px-6 text-center py-3">
                                SWIFT kód
                            </th>
                            <th scope="col" class="px-6 text-center py-3">
                                Od
                            </th>
                            <th scope="col" class="px-6 text-center py-3">
                                Do
                            </th>
                            <th scope="col" class="px-6 text-center py-3">
                                Akcie
                            </th>
                        </tr>
                    </thead>
                    <tbody id="accountsBody">
                    </tbody>
                </table>
                <button id="accountButton" data-modal-target="ucetModal" type="button" data-modal-toggle="ucetModal"
                    class="block text-center max-w-sm p-6 bg-white border border-gray-200 rounded-lg shadow hover:bg-gray-100 dark:bg-gray-800 dark:border-gray-700 dark:hover:bg-gray-700">
                    <svg class="w-6 h-6 text-gray-800 dark:text-white" style="margin: auto;" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 7.757v8.486M7.757 12h8.486M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                    </svg>
                    <h5 class="mb-2 text-2xl font-bold tracking-tight text-gray-900 dark:text-white">Neboli pridané
                        žiadne
                        účty</h5>
                    <p class="font-normal text-gray-700 dark:text-gray-400">Kliknite a pridajte účet ku klientovi</p>
                </button>
            </div>
            <div class="flex w-full mt-4 items-center justify-end">
                <button id="addAnother" data-modal-target="ucetModal" type="button" data-modal-toggle="ucetModal"
                    class="text-white hidden bg-green-700 hover:bg-green-800 focus:outline-none focus:ring-4 focus:ring-green-300 font-medium rounded-full text-sm px-5 py-2.5 text-center me-2 mb-2 dark:bg-green-600 dark:hover:bg-green-700 dark:focus:ring-green-800">Pridať
                    ďalší</button>
            </div>
        </div>
        <div class="">
            <div class="flex gap-10 w-full">
                <h3 class="text-lg mt-3 w-full font-bold dark:text-white">Kontaktné údaje</h3>
                <h3 class="text-lg mt-3 w-full px-8 font-bold dark:text-white">AML</h3>
            </div>
            <div class="flex mt-3 gap-8">
                <div class="flex w-full mt-3 gap-8">
                    <div class="w-full bg-white rounded-xl p-2 px-4 flex flex-col gap-1 shadow-md">
                        <div class="mb-1">
                            <label for="address"
                                class="block mb-2 text-sm w-1/2 font-semibold text-gray-900 dark:text-white">Ulica:
                            </label>
                            <input type="text" id="address" name="address"
                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                placeholder="Ulica" />
                        </div>
                        <div class="mb-1">
                            <label for="city"
                                class="block mb-2 text-sm w-1/2 font-semibold text-gray-900 dark:text-white">Mesto:
                            </label>
                            <input type="text" id="city" name="city"
                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                placeholder="Mesto" />
                        </div>
                        <div class="mb-1">
                            <label for="postalcode"
                                class="block mb-2 text-sm w-1/2 font-semibold text-gray-900 dark:text-white">PSČ:
                            </label>
                            <input type="text" id="postalcode" name="postalcode"
                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                placeholder="PSČ" />
                        </div>
                    </div>
                    <div class="w-full bg-white rounded-xl p-2 px-4 flex flex-col gap-1 shadow-md">
                        <div class="mb-1">
                            <label for="kontaktphonenumber"
                                class="block mb-2 text-sm w-1/2 font-semibold text-gray-900 dark:text-white">Telefón:
                            </label>
                            <input type="text" id="kontaktphonenumber" name="kontaktphonenumber"
                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                placeholder="Telefón" />
                        </div>
                        <div class="mb-1">
                            <label for="kontaktemail"
                                class="block mb-2 text-sm w-1/2 font-semibold text-gray-900 dark:text-white">Email:
                            </label>
                            <input type="text" id="kontaktemail" name="kontaktemail"
                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                placeholder="Email" />
                        </div>
                        <div id="kontaktnaOsoba" style="display: none;">
                            <label for="kontaktnapriez"
                                class="block mb-2 text-sm w-1/2 font-semibold text-gray-900 dark:text-white">Kontaktná
                                osoba: </label>
                            <input type="text" id="kontaktnapriez" name="kontaktnapriez"
                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                placeholder="Meno a priezvisko" />
                        </div>
                    </div>
                </div>
                <div class="w-full flex-col flex justify-between">
                    <div class="flex mt-3 gap-8">
                        <div class="w-full bg-white rounded-xl p-2 px-4 flex flex-col gap-1 shadow-md">
                            <div class="flex justify-between">
                                <label for="uroven_aml_rizika" id="politickyexponovana"
                                    class="block text-sm w-full font-semibold text-gray-900 dark:text-white">Politicky
                                    exponovaná osoba: </label>
                                <div class="flex items-center">
                                    <input id="is_pep" name="is_pep" type="checkbox"
                                        class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                                    <label for="is_pep"
                                        class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Áno</label>
                                </div>
                                </span>
                            </div>
                            <div class="flex justify-between items-center mb-1">
                                <label for="uroven_aml_rizika" id="amlRiziko"
                                    class="block text-sm w-full font-semibold text-gray-900 dark:text-white">Úroveň
                                    AML rizika: </label>
                                <select id="uroven_aml_rizika" name="uroven_aml_rizika" required
                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                    <option value="1">
                                        Nízke
                                    </option>
                                    <option value="2">
                                        Stredné
                                    </option>
                                    <option value="3">
                                        Vysoké
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <h3 class="text-lg mt-3 font-semibold dark:text-white">Služby tretích strán</h3>
                    <div class="flex mb-32 mt-3 gap-8">
                        <div class="w-full bg-white rounded-xl p-2 px-4 flex flex-col gap-1 shadow-md">
                            <div class="flex justify-between">
                                <strong>Refundácia dane</strong>
                                <div class="flex">
                                    <div class="flex items-center me-4">
                                        <input id="refundaciadane0" type="radio" name="refundaciadane" value="1"
                                            class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                                        <label for="refundaciadane0"
                                            class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Áno</label>
                                    </div>
                                    <div class="flex items-center me-4">
                                        <input id="refundaciadane1" type="radio" name="refundaciadane" value="0"
                                            class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                                        <label for="refundaciadane1"
                                            class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Nie</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="elektronickyOnboarding" class="joj" style="display: none;">
            <h3 class="text-lg mt-3 font-semibold">Elektronický onboarding</h3>
            <div class="w-full bg-white rounded-xl p-2 px-4 flex gap-16 shadow-md">
                <div class="w-full flex flex-col gap-2">
                    <div class="mb-1">
                        <label for="onboarding_id"
                            class="block mb-2 text-sm w-1/2 font-semibold text-gray-900 dark:text-white">Onboarding ID:
                        </label>
                        <input type="text" id="onboarding_id" name="onboarding_id"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" />
                    </div>
                    <div class="mb-1">
                        <label for="email_verified_at"
                            class="block mb-2 text-sm w-1/2 font-semibold text-gray-900 dark:text-white">Overenie
                            emailu:
                        </label>
                        <input type="text" id="email_verified_at" name="email_verified_at"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" />
                    </div>
                    <div class="mb-1">
                        <label for="created_at"
                            class="block mb-2 text-sm w-1/2 font-semibold text-gray-900 dark:text-white">Súhlas so
                            spracovaním osobných údajov: </label>
                        <input type="text" id="created_at" name="created_at"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" />
                    </div>
                </div>
                <div class="w-full flex flex-col gap-2">
                    <div class="mb-1">
                        <label for="vs_id"
                            class="block mb-2 text-sm w-1/2 font-semibold text-gray-900 dark:text-white">VS
                            prvej platby: </label>
                        <input type="text" id="vs_id" name="vs_id"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" />
                    </div>
                    <div class="mb-1">
                        <label for="pre_contractual_conditions"
                            class="block mb-2 text-sm w-1/2 font-semibold text-gray-900 dark:text-white">Súhlas s
                            predobchodnými informáciami: </label>
                        <input type="text" id="pre_contractual_conditions" name="pre_contractual_conditions"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" />
                    </div>
                    <div class="mb-1">
                        <label for="general_conditions"
                            class="block mb-2 text-sm font-semibold text-gray-900 dark:text-white">Súhlas s Obchodnými
                            podmienkami, cenníkm a investičnou stratégiou: </label>
                        <input type="text" id="general_conditions" name="general_conditions"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" />
                    </div>
                </div>
            </div>
        </div>
        <h3 class="text-lg mt-3 font-semibold dark:text-white">Výpisy</h3>
        <div class="flex mb-32 mt-3 gap-8">
            <div class="w-full bg-white rounded-xl p-2 px-4 flex flex-col gap-1 shadow-md">
                <div class="flex gap-3 items-center mb-1">
                    <label for="vypisfrekvencia"
                        class="block text-sm w-full font-semibold text-gray-900 dark:text-white">Frekvencia</label>
                    <select id="vypisfrekvencia" name="vypisfrekvencia"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                        <option value="1">
                            Mesačne
                        </option>
                        <option selected value="4">
                            Štvrťročne
                        </option>
                        <option value="6">
                            Polročne
                        </option>
                        <option value="12">
                            Ročne
                        </option>
                    </select>
                </div>
                <div class="flex gap-3 items-center mb-1">
                    <label for="gfi" class="block text-sm w-full font-semibold text-gray-900 dark:text-white">Je
                        súčasťou
                        GFI</label>
                    <select id="gfi" name="gfi"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                        <option value="1">
                            Áno
                        </option>
                        <option value="0">
                            Nie
                        </option>
                        <option value="2">
                            Spriaznená osoba
                        </option>
                    </select>
                </div>
            </div>
            <div class="w-full bg-white rounded-xl p-2 px-4 flex flex-col gap-1 shadow-md">
                <div id="dan_domicil_wrapper" class="flex gap-3 text-gray-900 dark:text-white items-center mb-1">
                    <label for="dan_domicil" class="block text-sm w-full font-semibold">Daňový
                        domicil</label>
                    <select id="dan_domicil" name="dan_domicil"
                        class="bg-gray-50 border border-gray-300 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:focus:ring-blue-500 dark:focus:border-blue-500">
                        <?php
                        foreach ($states as $state) { ?>
                            <option value="<?php echo $state["stateid"]; ?>"><?php echo $state["stateall"] ?></option>
                        <?php } ?>
                    </select>
                </div>
                <div class="flex gap-3 items-center mb-1">
                    <label for="typzdanenia"
                        class="block text-sm w-full font-semibold text-gray-900 dark:text-white">Typ
                        zdanenia</label>
                    <select id="typzdanenia" name="typzdanenia"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                        required>
                        <option value="FO">
                            FO
                        </option>
                        <option value="PO">
                            PO
                        </option>
                        <option value="NO">
                            NO
                        </option>
                    </select>
                </div>
            </div>
        </div>
        <h3 class="text-lg mt-3 font-semibold dark:text-white">Predajné miesto</h3>
        <div class="flex mb-32 mt-3 gap-8">
            <div class="w-full bg-white rounded-xl p-2 px-4 flex flex-col gap-1 shadow-md">
                <div class="flex gap-3 items-center mb-1">
                    <label for="pobocka"
                        class="block text-sm w-full font-semibold text-gray-900 dark:text-white">Priradený
                        pracovník</label>
                    <select id="pobocka" value="" name="pobocka"
                        class="bg-gray-300 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1"
                        disabled="disabled">
                        <?php
                        foreach ($pobocka as $pobo) { ?>
                            <option value="<?php echo $pobo['pobockaid']; ?>">
                                <?php echo $pobo["pobockaname"] . " " . $pobo["address"] . ", " . $pobo["postalcode"] ?>
                            </option>
                        <?php } ?>
                    </select>
                </div>
                <div class="flex gap-3 items-center mb-1">
                    <label for="userid"
                        class="block text-sm w-full font-semibold text-gray-900 dark:text-white">Priradený
                        pracovník</label>
                    <select id="userid" name="userid"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                        required>
                        <?php
                        foreach ($users as $user) { ?>
                            <option value="<?php echo $user['userid'] ?>"><?php echo $user["username"] ?></option>
                        <?php } ?>
                    </select>
                </div>
            </div>
            <div class="w-full bg-white rounded-xl p-2 px-4 flex flex-col gap-1 shadow-md">
                <div class="flex mb-1">
                    <label for="datetimeagreement"
                        class="block mb-2 text-sm w-1/2 font-semibold text-gray-900 dark:text-white">Dátum
                        podania: </label>
                    <input type="date" id="datetimeagreement" name="datetimeagreement"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                        placeholder="Dátum podania" required />
                </div>
                <div class="flex mb-1">
                    <label for="timeagreement"
                        class="block mb-2 text-sm w-1/2 font-semibold text-gray-900 dark:text-white">Čas
                        podania: </label>
                    <input type="time" id="timeagreement" name="timeagreement"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1 px-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                        placeholder="Čas podania" />
                </div>
            </div>
        </div>
        <div class="flex sticky z-40 mt-6 shadow-md border border-gray-200 rounded-lg px-5 w-full bg-white p-3 items-center"
            style="bottom: 2rem;">
            <button type="submit"
                class="focus:outline-none text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-green-600 dark:hover:bg-green-700 dark:focus:ring-green-800">
                Vytvoriť nového klienta
            </button>
        </div>
    </form>
</section>
<div id="ucetModal" tabindex="-1" aria-hidden="true"
    class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative p-4 w-full max-w-2xl max-h-full">
        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
            <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                    Tvorba nového bežného účtu
                </h3>
                <button type="button"
                    class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                    data-modal-hide="ucetModal">
                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                        viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                    </svg>
                    <span class="sr-only">Zavrieť</span>
                </button>
            </div>
            <form id="newMoneyAccount">
                <div class="p-4 md:p-5 space-y-4">
                    <div class="flex gap-8">
                        <div class="w-full">
                            <label for="iban" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">IBAN
                                účtu:</label>
                            <input type="text" id="iban" name="iban"
                                class="block w-full p-2 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label for="mena" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Mena
                                účtu:</label>
                            <select id="mena" name="mena"
                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full">
                                <option selected>Mena</option>
                                <?php foreach ($meny as $key => $value) { ?>
                                    <option value="<?php echo $value["mena"]; ?>">
                                        <?php echo "[" . $value["mena"] . "] " . $value["menanaz"]; ?>
                                    </option>
                                <?php } ?>
                            </select>
                        </div>
                    </div>
                    <div class="w-full">
                        <label for="swift" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">SWIFT
                            kód (nepovinné):</label>
                        <input type="text" id="swift" name="swift"
                            class="block w-full p-2 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 text-xs focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div class="flex items-center pt-3 justify-center w-full">
                        <label for="dropzone-file"
                            class="flex flex-col items-center justify-center w-full h-64 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:hover:bg-bray-800 dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500 dark:hover:bg-gray-600">
                            <div class="flex flex-col items-center justify-center pt-5 pb-6">
                                <svg class="w-8 h-8 mb-4 text-gray-500 dark:text-gray-400" aria-hidden="true"
                                    xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2" />
                                </svg>
                                <p class="mb-2 text-sm text-gray-500 dark:text-gray-400"><span
                                        class="font-semibold">Klikni pre nahranie súboru</span> alebo presuň súbor sem
                                </p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Max. (5MB)</p>
                            </div>
                            <input id="dropzone-file" type="file" class="hidden" />
                        </label>
                    </div>
                    <div class="flex items-center py-3 border-t border-gray-200 rounded-b dark:border-gray-600">
                        <button data-modal-hide="ucetModal" type="submit"
                            class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">Vytvoriť
                            účet</button>
                        <button data-modal-hide="ucetModal" type="button"
                            class="py-2.5 px-5 ms-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">Zavrieť</button>
                    </div>
            </form>
        </div>
    </div>
</div>
<script src="/src/assets/js/klienti/create.js"></script>
<script src="/src/assets/js/klienti/onboarding.js"></script>